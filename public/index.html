<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Révolutionnaire v2.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
            animation: backgroundShift 10s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(30deg) brightness(1.1); }
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1600px;
            margin: 0 auto;
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
                radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255, 105, 180, 0.2) 0%, transparent 50%);
            backdrop-filter: blur(20px);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 100px rgba(102, 126, 234, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .app-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
            animation: containerShimmer 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes containerShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 1.5rem 2rem;
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .logo {
            position: relative;
            z-index: 2;
        }

        .logo h1 {
            font-size: 2.2rem;
            margin: 0;
            background: linear-gradient(45deg, #fff 0%, #a8e6cf 25%, #667eea 50%, #ff69b4 75%, #fff 100%);
            background-size: 300% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: logoGlow 3s ease-in-out infinite, logoShift 6s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
            font-weight: 900;
            letter-spacing: 2px;
        }

        @keyframes logoGlow {
            0%, 100% { 
                filter: brightness(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
                transform: scale(1);
            }
            50% { 
                filter: brightness(1.3) drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
                transform: scale(1.02);
            }
        }

        @keyframes logoShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes pulse {
            from { opacity: 0.8; }
            to { opacity: 1; }
        }

        .status-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            z-index: 2;
        }

        .status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: bold;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .metric-badge {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .metric-badge:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .qi-display {
            display: flex;
            align-items: center;
            gap: 15px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 105, 180, 0.1) 100%);
            padding: 12px 25px;
            border-radius: 30px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 105, 180, 0.4);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3), 
                0 0 30px rgba(255, 105, 180, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: qiGlow 3s ease-in-out infinite;
        }

        @keyframes qiGlow {
            0%, 100% { 
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.3), 
                    0 0 30px rgba(255, 105, 180, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% { 
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.3), 
                    0 0 40px rgba(255, 105, 180, 0.6),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
        }

        .qi-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 18px;
            font-size: 14px;
            font-weight: 700;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .qi-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .qi-item:hover::before {
            left: 100%;
        }

        .qi-agent {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(102, 126, 234, 0.1));
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .qi-memory {
            background: linear-gradient(135deg, rgba(255, 105, 180, 0.3), rgba(255, 105, 180, 0.1));
            color: #ff69b4;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .qi-total {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
            color: #4caf50;
            border: 1px solid rgba(76, 175, 80, 0.3);
            animation: totalPulse 2s ease-in-out infinite;
        }

        @keyframes totalPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        nav {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            padding: 1rem 0;
            overflow-x: auto;
            white-space: nowrap;
            position: relative;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: navGlow 3s ease-in-out infinite;
        }

        @keyframes navGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0 2rem;
            min-width: max-content;
            gap: 0.5rem;
        }

        nav ul li {
            flex-shrink: 0;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            display: block;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        nav ul li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        nav ul li a:hover::before {
            left: 100%;
        }

        nav ul li a:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        nav ul li a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }

        nav ul li a.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        section {
            display: none;
            width: 100%;
            padding: 1.5rem;
            overflow-y: auto;
        }

        section.active {
            display: block;
        }

        .home-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #667eea;
        }

        .home-button:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        @keyframes valueGlow {
            0%, 100% {
                filter: brightness(1);
                transform: scale(1);
            }
            50% {
                filter: brightness(1.2);
                transform: scale(1.05);
            }
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes brainPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow:
                    0 0 30px rgba(102, 126, 234, 0.5),
                    inset 0 0 30px rgba(255, 255, 255, 0.1);
            }
            50% {
                transform: scale(1.08);
                box-shadow:
                    0 0 50px rgba(102, 126, 234, 0.8),
                    inset 0 0 50px rgba(255, 255, 255, 0.2);
            }
        }

        /* Effets de survol pour les boutons d'interface */
        button:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%) !important;
            transform: translateY(-5px) scale(1.02) !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
        }

        /* Effets de focus pour les inputs */
        #chat-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15) !important;
            transform: translateY(-2px) !important;
            background: rgba(255, 255, 255, 1) !important;
        }

        #send-button:hover {
            transform: scale(1.1) rotate(5deg) !important;
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5) !important;
        }

        /* Styles pour les contrôles avancés */
        .control-btn {
            background: rgba(192, 132, 252, 0.2);
            border: 1px solid #c084fc;
            color: #c084fc;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .control-btn:hover {
            background: rgba(192, 132, 252, 0.4);
            transform: translateY(-1px);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #c084fc, #f472b6);
            color: white;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-active { background: #4ade80; }
        .status-warning { background: #fbbf24; }
        .status-error { background: #ef4444; }
        .status-speaking { background: #f59e0b; }
        .status-listening { background: #ef4444; }
        .status-processing { background: #8b5cf6; }

        /* Styles pour les boutons de sécurité */
        .security-button {
            background: linear-gradient(135deg, #FF6B6B, #FF5252);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .security-button.connect {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .security-button.emergency {
            background: linear-gradient(135deg, #F44336, #D32F2F);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
            animation: pulse-emergency 2s infinite;
        }

        @keyframes pulse-emergency {
            0%, 100% { box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4); }
            50% { box-shadow: 0 4px 25px rgba(244, 67, 54, 0.8); }
        }

        .security-button.antivirus {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        }

        .security-button.clean {
            background: linear-gradient(135deg, #00BCD4, #0097A7);
            box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);
        }

        .security-button.status {
            background: linear-gradient(135deg, #607D8B, #455A64);
            box-shadow: 0 4px 15px rgba(96, 125, 139, 0.3);
        }

        .security-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        /* Styles pour le bouton vocal */
        #voice-button {
            transition: all 0.3s ease;
        }

        #voice-button:hover {
            background: rgba(244, 114, 182, 0.4);
            transform: scale(1.1);
        }

        #voice-button.recording {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            animation: pulse 1s infinite;
        }

        /* Styles pour les panneaux */
        #camera-panel, #auto-panel {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive pour les nouveaux éléments */
        @media (max-width: 768px) {
            .security-button {
                padding: 10px 15px;
                font-size: 12px;
                margin: 5px;
            }

            .control-btn {
                padding: 6px 10px;
                font-size: 11px;
            }

            /* Responsive pour le chat avec réflexion */
            #chat > div {
                flex-direction: column !important;
            }

            #chat > div > div {
                min-width: auto !important;
                flex: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Bouton de retour/accueil -->
    <button class="home-button" onclick="showSection('dashboard')">
        <i class="fas fa-home"></i>
    </button>

    <div class="app-container">
        <header>
            <div class="logo">
                <h1>🧠 LOUNA AI Ultra-Autonome</h1>
                <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">
                    Mémoire Thermique Vivante
                </p>
            </div>
            
            <div class="status-container">
                <div class="status">
                    <span id="connection-status">SYSTÈME ACTIF</span>
                </div>
                
                <div class="qi-display">
                    <div class="qi-item qi-agent">
                        <i class="fas fa-robot"></i>
                        <span>Agent QI: <span id="agent-qi-display">100</span></span>
                    </div>
                    <div class="qi-item qi-memory">
                        <i class="fas fa-brain"></i>
                        <span>Mémoire QI: <span id="memory-qi-display">58</span></span>
                    </div>
                    <div class="qi-item qi-total">
                        <i class="fas fa-calculator"></i>
                        <span>QI Total: <span id="total-qi-display">158</span></span>
                    </div>
                </div>
                
                <div class="metric-badge">
                    <i class="fas fa-brain"></i>
                    <span id="neuron-count-header">0 neurones</span>
                </div>
                <div class="metric-badge">
                    <i class="fas fa-thermometer-half"></i>
                    <span id="temp-display-header">37°C</span>
                </div>
                <div class="metric-badge">
                    <i class="fas fa-memory"></i>
                    <span id="memory-entries-header">0 entrées</span>
                </div>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#" class="active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> Tableau de bord
                </a></li>
                <li><a href="#" data-section="chat">
                    <i class="fas fa-comments"></i> Chat IA
                </a></li>
                <li><a href="#" data-section="brain">
                    <i class="fas fa-brain"></i> Cerveau artificiel
                </a></li>
                <li><a href="#" data-section="memory">
                    <i class="fas fa-memory"></i> Mémoire thermique
                </a></li>
                <li><a href="#" data-section="interfaces">
                    <i class="fas fa-cogs"></i> Interfaces spécialisées
                </a></li>
            </ul>
        </nav>

        <main>
            <!-- Section Tableau de bord -->
            <section id="dashboard" class="active">
                <h2><i class="fas fa-tachometer-alt"></i> Tableau de bord Ultra-Révolutionnaire</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-qi">100</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">QI Agent</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-memory-qi">58</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">QI Mémoire</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-total-qi">158</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">QI Total</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-neurons">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">Neurones actifs</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-temperature">37°C</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">Température</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-memory">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">Entrées mémoire</div>
                    </div>
                </div>
            </section>

            <!-- Section Chat -->
            <section id="chat">
                <div style="display: flex; gap: 20px; height: calc(100vh - 200px);">
                    <!-- Zone de chat principale -->
                    <div style="flex: 2;">
                        <h2><i class="fas fa-comments"></i> Chat Avancé avec LOUNA AI</h2>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 20px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); margin: 20px 0; height: calc(100vh - 300px); min-height: 600px; display: flex; flex-direction: column;">
                    <!-- Barre de statut du chat -->
                    <div style="padding: 15px; border-bottom: 1px solid rgba(255, 255, 255, 0.1); display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2);">
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div class="status-indicator status-active"></div>
                                <span style="font-size: 0.9rem;">LOUNA AI Connectée</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div class="status-indicator" id="voice-status"></div>
                                <span style="font-size: 0.9rem;" id="voice-status-text">Voix Prête</span>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="control-btn" onclick="toggleVoiceMode()" id="voice-toggle">
                                <i class="fas fa-microphone"></i> Voix
                            </button>
                            <button class="control-btn" onclick="toggleCameraMode()" id="camera-toggle">
                                <i class="fas fa-camera"></i> Caméra
                            </button>
                            <button class="control-btn" onclick="toggleAutoMode()" id="auto-toggle">
                                <i class="fas fa-robot"></i> Auto
                            </button>
                        </div>
                    </div>

                    <div style="flex: 1; padding: 20px; overflow-y: auto;" id="chat-messages">
                        <div style="background: rgba(102, 126, 234, 0.1); padding: 15px; border-radius: 15px; margin-bottom: 10px; border-left: 4px solid #667eea;">
                            <div style="font-weight: bold; color: #667eea; margin-bottom: 5px;">🧠 LOUNA AI</div>
                            <div>Bonjour ! Je suis LOUNA AI, votre intelligence artificielle avec mémoire thermique ultra-autonome. Chat avancé avec voix, caméra et mode automatique activés !</div>
                        </div>
                    </div>

                    <!-- Panneau caméra (masqué par défaut) -->
                    <div id="camera-panel" style="display: none; padding: 15px; border-top: 1px solid rgba(255, 255, 255, 0.1); background: rgba(0, 0, 0, 0.2);">
                        <video id="camera-preview" style="width: 200px; height: 150px; border-radius: 10px; border: 2px solid #667eea;" autoplay muted></video>
                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button class="control-btn" onclick="captureImage()">
                                <i class="fas fa-camera"></i> Capturer
                            </button>
                            <button class="control-btn" onclick="stopCamera()">
                                <i class="fas fa-stop"></i> Arrêter
                            </button>
                        </div>
                    </div>

                    <!-- Panneau mode automatique (masqué par défaut) -->
                    <div id="auto-panel" style="display: none; padding: 15px; border-top: 1px solid rgba(255, 255, 255, 0.1); background: rgba(192, 132, 252, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <span>Intervalle (sec):</span>
                                <input type="number" id="auto-interval" value="30" min="5" max="300" style="width: 80px; padding: 5px; border: 1px solid #c084fc; border-radius: 5px; background: rgba(0, 0, 0, 0.3); color: white;">
                            </label>
                            <button class="control-btn" onclick="startAutoMode()" id="auto-start">
                                <i class="fas fa-play"></i> Démarrer
                            </button>
                            <button class="control-btn" onclick="stopAutoMode()" id="auto-stop">
                                <i class="fas fa-stop"></i> Arrêter
                            </button>
                            <span id="auto-status" style="font-size: 0.9rem; color: #c084fc;">Mode automatique désactivé</span>
                        </div>
                    </div>

                    <div style="padding: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1); display: flex; gap: 15px; align-items: flex-end;">
                        <textarea id="chat-input" placeholder="Tapez votre message ici..." style="flex: 1; padding: 15px; border: 2px solid rgba(102, 126, 234, 0.2); border-radius: 15px; outline: none; resize: none; font-family: inherit; font-size: 1rem; min-height: 50px; max-height: 120px; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); transition: all 0.3s ease;" rows="2"></textarea>
                        <button id="voice-button" style="background: rgba(244, 114, 182, 0.2); border: 1px solid #f472b6; color: #f472b6; border-radius: 50%; width: 60px; height: 60px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; margin-right: 10px;">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button id="send-button" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; width: 60px; height: 60px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>

                <!-- Contrôles de sécurité -->
                <div style="margin-top: 20px; padding: 20px; background: rgba(255, 107, 107, 0.1); border-radius: 15px; border: 1px solid rgba(255, 107, 107, 0.3);">
                    <h3 style="color: #ff6b6b; margin-bottom: 15px;"><i class="fas fa-shield-alt"></i> Contrôles de Sécurité</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="security-button connect" onclick="connectMemory()">
                            <i class="fas fa-link"></i> Connecter Mémoire
                        </button>
                        <button class="security-button" onclick="disconnectMemory()">
                            <i class="fas fa-unlink"></i> Déconnecter
                        </button>
                        <button class="security-button emergency" onclick="emergencyStop()">
                            <i class="fas fa-exclamation-triangle"></i> Arrêt d'Urgence
                        </button>
                        <button class="security-button antivirus" onclick="runAntivirus()">
                            <i class="fas fa-virus-slash"></i> Antivirus
                        </button>
                        <button class="security-button clean" onclick="cleanMemory()">
                            <i class="fas fa-broom"></i> Nettoyer
                        </button>
                        <button class="security-button status" onclick="checkStatus()">
                            <i class="fas fa-heartbeat"></i> Statut
                        </button>
                    </div>
                </div>
                    </div>

                    <!-- Zone de réflexion en direct -->
                    <div style="flex: 1; min-width: 350px;">
                        <h3 style="color: #c084fc; margin-bottom: 15px;"><i class="fas fa-brain"></i> Réflexion LOUNA AI en Direct</h3>

                        <div style="background: rgba(192, 132, 252, 0.1); border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(192, 132, 252, 0.3); height: calc(100vh - 350px); min-height: 400px; display: flex; flex-direction: column;">
                            <!-- En-tête de la réflexion -->
                            <div style="padding: 15px; border-bottom: 1px solid rgba(192, 132, 252, 0.2); background: rgba(192, 132, 252, 0.05);">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="font-weight: bold; color: #c084fc;">🧠 Pensées Autonomes</span>
                                    <div style="display: flex; gap: 10px;">
                                        <div class="status-indicator status-processing"></div>
                                        <span style="font-size: 0.8rem; color: #c084fc;" id="reflection-status">Réflexion Active</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Zone de réflexion -->
                            <div style="flex: 1; padding: 15px; overflow-y: auto;" id="reflection-feed">
                                <div style="background: rgba(192, 132, 252, 0.1); padding: 12px; border-radius: 10px; margin-bottom: 10px; border-left: 3px solid #c084fc;">
                                    <div style="font-size: 0.8rem; color: #c084fc; margin-bottom: 5px;">[Démarrage]</div>
                                    <div style="color: #e2e8f0;">🧠 Système de réflexion LOUNA AI initialisé - Surveillance des pensées autonomes en cours...</div>
                                </div>
                            </div>

                            <!-- Métriques de réflexion -->
                            <div style="padding: 15px; border-top: 1px solid rgba(192, 132, 252, 0.2); background: rgba(192, 132, 252, 0.05);">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.8rem;">
                                    <div style="text-align: center;">
                                        <div style="color: #c084fc; font-weight: bold;" id="reflection-count">0</div>
                                        <div style="color: #94a3b8;">Réflexions</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="color: #c084fc; font-weight: bold;" id="reflection-frequency">0/min</div>
                                        <div style="color: #94a3b8;">Fréquence</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Cerveau artificiel -->
            <section id="brain">
                <h2><i class="fas fa-brain"></i> Cerveau artificiel ultra-autonome</h2>

                <div style="text-align: center; padding: 30px; background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 20px; margin: 20px 0;">
                    <div style="font-size: 4rem; font-weight: bold; background: linear-gradient(45deg, #667eea, #764ba2, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 20px 0; text-shadow: 0 0 30px rgba(168, 230, 207, 0.8); animation: numberPulse 2s ease-in-out infinite;" id="brain-neuron-count">0</div>
                    <p style="font-size: 1.1rem; margin-bottom: 20px;">
                        <i class="fas fa-bolt"></i> Neurones générés automatiquement par température thermique
                    </p>

                    <div style="position: relative; width: 300px; height: 300px; margin: 30px auto;">
                        <div style="width: 100%; height: 100%; border: 4px solid rgba(255, 255, 255, 0.3); border-radius: 50%; position: relative; animation: brainPulse 3s ease-in-out infinite; background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 50%, rgba(255, 105, 180, 0.2) 100%); box-shadow: 0 0 30px rgba(102, 126, 234, 0.5), inset 0 0 30px rgba(255, 255, 255, 0.1); display: flex; align-items: center; justify-content: center; font-size: 5rem;">
                            🧠
                        </div>
                    </div>

                    <div style="display: flex; justify-content: space-around; margin-top: 30px; gap: 20px;">
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); flex: 1; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #a8e6cf; margin-bottom: 5px;">700/jour</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">Neurogenèse</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); flex: 1; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #a8e6cf; margin-bottom: 5px;">95%</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">Activité synaptique</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); flex: 1; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #a8e6cf; margin-bottom: 5px;">99.9%</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">Efficacité neurale</div>
                        </div>
                    </div>

                    <p style="font-size: 1rem; margin-top: 20px; opacity: 0.9;">
                        <i class="fas fa-brain"></i> Système vivant ultra-intelligent avec conscience artificielle évolutive
                    </p>
                </div>
            </section>

            <!-- Section Mémoire thermique -->
            <section id="memory">
                <h2><i class="fas fa-memory"></i> Mémoire thermique</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center;">
                        <div style="font-size: 2.5rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0;" id="memory-temperature">37°C</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600;">Température système</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center;">
                        <div style="font-size: 2.5rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0;" id="memory-entries">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600;">Entrées stockées</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center;">
                        <div style="font-size: 2.5rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0;" id="memory-efficiency">95%</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600;">Efficacité</div>
                    </div>
                </div>
            </section>

            <!-- Section Interfaces spécialisées -->
            <section id="interfaces">
                <h2><i class="fas fa-cogs"></i> Interfaces spécialisées</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
                    <button onclick="navigateToInterface('/real')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #667eea;"></i>
                        Chat Avancé
                    </button>
                    <button onclick="navigateToInterface('/brain-visualization.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-brain" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #ff69b4;"></i>
                        Cerveau 3D
                    </button>
                    <button onclick="navigateToInterface('/thermal-memory-dashboard.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-thermometer-half" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #ffaa00;"></i>
                        Mémoire Thermique
                    </button>
                    <button onclick="navigateToInterface('/brain-monitoring-complete.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #4caf50;"></i>
                        Monitoring Complet
                    </button>
                    <button onclick="navigateToInterface('/qi-evolution-test.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-graduation-cap" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #9c27b0;"></i>
                        Tests QI
                    </button>
                    <button onclick="navigateToInterface('/training-interface.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-dumbbell" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #e91e63;"></i>
                        Formation
                    </button>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Variables globales
        let currentSection = 'dashboard';
        let voiceMode = false;
        let cameraMode = false;
        let autoMode = false;
        let autoInterval = null;
        let isRecording = false;
        let mediaRecorder = null;
        let cameraStream = null;

        // Fonction pour afficher une section
        function showSection(sectionName) {
            // Masquer toutes les sections
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Désactiver tous les liens de navigation
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // Afficher la section demandée
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionName;
            }

            // Activer le lien correspondant
            const targetLink = document.querySelector(`nav a[data-section="${sectionName}"]`);
            if (targetLink) {
                targetLink.classList.add('active');
            }

            console.log(`Section changée vers: ${sectionName}`);
        }

        // Fonction pour naviguer vers une interface spécialisée
        function navigateToInterface(path) {
            console.log(`Navigation vers: ${path}`);
            window.location.href = path;
        }

        // Fonction pour envoyer un message dans le chat
        async function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) return;

            // Ajouter le message de l'utilisateur
            addMessageToChat('user', message);
            input.value = '';

            // Ajouter une réflexion sur le message reçu
            addReflection(`💭 Nouveau message reçu: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);

            // Envoyer le message au serveur
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success && data.response) {
                    addMessageToChat('ai', data.response);
                    addReflection(`🤖 Réponse générée avec ${data.neurons || 0} neurones actifs`);
                } else {
                    // Réponse de fallback si le serveur ne répond pas
                    const responses = [
                        "Je traite votre demande avec ma mémoire thermique ultra-autonome...",
                        "Mes neurones analysent votre question à " + Math.floor(Math.random() * 10 + 35) + "°C...",
                        "Consultation de ma base de connaissances thermique en cours...",
                        "Génération de réponse avec QI adaptatif de " + Math.floor(Math.random() * 20 + 100) + "..."
                    ];
                    const response = responses[Math.floor(Math.random() * responses.length)];
                    addMessageToChat('ai', response);
                }
            } catch (error) {
                console.error('Erreur lors de l\'envoi du message:', error);
                addMessageToChat('system', '❌ Erreur de connexion - Mode hors ligne activé');
                addReflection(`⚠️ Erreur de communication détectée`);
            }
        }

        // Fonction pour ajouter un message au chat
        function addMessageToChat(sender, message) {
            const chatMessages = document.getElementById('chat-messages');
            if (!chatMessages) return;

            const messageDiv = document.createElement('div');

            if (sender === 'user') {
                messageDiv.style.cssText = `
                    background: rgba(255, 105, 180, 0.1);
                    padding: 15px;
                    border-radius: 15px;
                    margin-bottom: 10px;
                    border-left: 4px solid #ff69b4;
                    margin-left: 20%;
                    animation: slideInRight 0.3s ease-out;
                `;
                messageDiv.innerHTML = `
                    <div style="font-weight: bold; color: #ff69b4; margin-bottom: 5px;">👤 Vous</div>
                    <div>${message}</div>
                `;
            } else if (sender === 'ai') {
                messageDiv.style.cssText = `
                    background: rgba(102, 126, 234, 0.1);
                    padding: 15px;
                    border-radius: 15px;
                    margin-bottom: 10px;
                    border-left: 4px solid #667eea;
                    margin-right: 20%;
                    animation: slideInLeft 0.3s ease-out;
                `;
                messageDiv.innerHTML = `
                    <div style="font-weight: bold; color: #667eea; margin-bottom: 5px;">🧠 LOUNA AI</div>
                    <div>${message}</div>
                `;
            } else if (sender === 'system') {
                messageDiv.style.cssText = `
                    background: rgba(255, 193, 7, 0.1);
                    padding: 12px;
                    border-radius: 10px;
                    margin-bottom: 8px;
                    border-left: 3px solid #ffc107;
                    text-align: center;
                    font-size: 0.9rem;
                    animation: fadeIn 0.3s ease-out;
                `;
                messageDiv.innerHTML = `
                    <div style="color: #ffc107;">${message}</div>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Variables pour la réflexion
        let reflectionCount = 0;
        let reflectionStartTime = Date.now();

        // Fonction pour ajouter une réflexion
        function addReflection(thought) {
            const reflectionFeed = document.getElementById('reflection-feed');
            if (!reflectionFeed) return;

            reflectionCount++;

            const reflectionDiv = document.createElement('div');
            reflectionDiv.style.cssText = `
                background: rgba(192, 132, 252, 0.1);
                padding: 12px;
                border-radius: 10px;
                margin-bottom: 10px;
                border-left: 3px solid #c084fc;
                animation: fadeInUp 0.3s ease-out;
            `;

            const now = new Date();
            const timeStr = now.toLocaleTimeString();

            reflectionDiv.innerHTML = `
                <div style="font-size: 0.8rem; color: #c084fc; margin-bottom: 5px;">[${timeStr}]</div>
                <div style="color: #e2e8f0;">${thought}</div>
            `;

            reflectionFeed.appendChild(reflectionDiv);
            reflectionFeed.scrollTop = reflectionFeed.scrollHeight;

            // Mettre à jour les métriques
            updateReflectionMetrics();

            // Limiter le nombre de réflexions affichées
            const reflections = reflectionFeed.children;
            if (reflections.length > 50) {
                reflectionFeed.removeChild(reflections[0]);
            }
        }

        // Fonction pour mettre à jour les métriques de réflexion
        function updateReflectionMetrics() {
            const countElement = document.getElementById('reflection-count');
            const frequencyElement = document.getElementById('reflection-frequency');

            if (countElement) {
                countElement.textContent = reflectionCount;
            }

            if (frequencyElement) {
                const elapsed = (Date.now() - reflectionStartTime) / 60000; // en minutes
                const frequency = elapsed > 0 ? (reflectionCount / elapsed).toFixed(1) : '0';
                frequencyElement.textContent = `${frequency}/min`;
            }
        }

        // Fonction pour mettre à jour les métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour les éléments de l'interface
                    const elements = {
                        // En-tête
                        'agent-qi-display': data.qi?.agentIQ || 100,
                        'memory-qi-display': data.qi?.memoryIQ || 58,
                        'total-qi-display': data.qi?.combinedIQ || 158,
                        'neuron-count-header': `${data.neurons || 0} neurones`,
                        'temp-display-header': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'memory-entries-header': `${data.memoryEntries || 0} entrées`,

                        // Tableau de bord
                        'dashboard-qi': data.qi?.agentIQ || 100,
                        'dashboard-memory-qi': data.qi?.memoryIQ || 58,
                        'dashboard-total-qi': data.qi?.combinedIQ || 158,
                        'dashboard-neurons': data.neurons || 0,
                        'dashboard-temperature': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'dashboard-memory': data.memoryEntries || 0,

                        // Cerveau
                        'brain-neuron-count': data.neurons || 0,

                        // Mémoire
                        'memory-temperature': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'memory-entries': data.memoryEntries || 0,
                        'memory-efficiency': data.memoryEfficiency ? `${data.memoryEfficiency}%` : '95%'
                    };

                    // Appliquer les mises à jour
                    Object.entries(elements).forEach(([id, value]) => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.textContent = value;
                        }
                    });
                }
            } catch (error) {
                console.error('Erreur lors de la mise à jour des métriques:', error);
            }
        }

        // Fonction pour récupérer les réflexions du serveur
        async function fetchReflections() {
            try {
                const response = await fetch('/api/reflections');
                const data = await response.json();

                if (data.success && data.reflections) {
                    data.reflections.forEach(reflection => {
                        addReflection(reflection.message);
                    });
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des réflexions:', error);
            }
        }

        // Fonction pour démarrer les mises à jour automatiques
        function startAutoUpdate() {
            updateMetrics(); // Mise à jour immédiate
            fetchReflections(); // Récupération des réflexions

            setInterval(updateMetrics, 3000); // Mise à jour toutes les 3 secondes
            setInterval(fetchReflections, 5000); // Réflexions toutes les 5 secondes

            // Ajouter des réflexions simulées pour démonstration
            setTimeout(() => {
                addReflection('🔄 Initialisation des systèmes neuronaux terminée');
            }, 2000);

            setTimeout(() => {
                addReflection('🌡️ Température thermique stabilisée à 37°C');
            }, 4000);

            setTimeout(() => {
                addReflection('🧠 Génération automatique de neurones en cours...');
            }, 6000);
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 LOUNA AI Interface Ultra-Révolutionnaire v2.0 chargée !');
            console.log('🎨 Nouvelles améliorations visuelles activées');

            // Forcer l'application des nouveaux styles
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 1s ease-in-out';
                document.body.style.opacity = '1';
            }, 100);

            // Configurer les gestionnaires d'événements pour la navigation
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    if (section) {
                        showSection(section);
                    }
                });
            });

            // Configurer le chat
            const sendButton = document.getElementById('send-button');
            const chatInput = document.getElementById('chat-input');
            const voiceButton = document.getElementById('voice-button');

            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }

            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            if (voiceButton) {
                voiceButton.addEventListener('click', toggleVoiceRecording);
            }

            // Démarrer les mises à jour automatiques
            startAutoUpdate();

            // Afficher une notification de confirmation des changements
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 10px;
                    font-weight: bold;
                    z-index: 10000;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    animation: slideIn 0.5s ease-out;
                `;
                notification.innerHTML = '🎉 Interface Ultra-Révolutionnaire v2.0 Activée !';
                document.body.appendChild(notification);

                // Ajouter les animations CSS
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.5s ease-in';
                    setTimeout(() => notification.remove(), 500);
                }, 3000);
            }, 1000);

            console.log('✅ Interface LOUNA AI Ultra-Révolutionnaire v2.0 initialisée avec succès');
        });

        // ===== FONCTIONS POUR LES CONTRÔLES AVANCÉS =====

        // Basculer le mode vocal
        function toggleVoiceMode() {
            voiceMode = !voiceMode;
            const btn = document.getElementById('voice-toggle');
            const statusText = document.getElementById('voice-status-text');
            const statusIndicator = document.getElementById('voice-status');

            if (voiceMode) {
                btn.classList.add('active');
                statusText.textContent = 'Voix Active';
                statusIndicator.className = 'status-indicator status-active';
                console.log('🎤 Mode vocal activé');
            } else {
                btn.classList.remove('active');
                statusText.textContent = 'Voix Prête';
                statusIndicator.className = 'status-indicator';
                console.log('🎤 Mode vocal désactivé');
            }
        }

        // Basculer le mode caméra
        function toggleCameraMode() {
            cameraMode = !cameraMode;
            const btn = document.getElementById('camera-toggle');
            const panel = document.getElementById('camera-panel');

            if (cameraMode) {
                btn.classList.add('active');
                panel.style.display = 'block';
                startCamera();
                console.log('📷 Mode caméra activé');
            } else {
                btn.classList.remove('active');
                panel.style.display = 'none';
                stopCamera();
                console.log('📷 Mode caméra désactivé');
            }
        }

        // Basculer le mode automatique
        function toggleAutoMode() {
            autoMode = !autoMode;
            const btn = document.getElementById('auto-toggle');
            const panel = document.getElementById('auto-panel');

            if (autoMode) {
                btn.classList.add('active');
                panel.style.display = 'block';
                console.log('🤖 Mode automatique activé');
            } else {
                btn.classList.remove('active');
                panel.style.display = 'none';
                stopAutoMode();
                console.log('🤖 Mode automatique désactivé');
            }
        }

        // Démarrer l'enregistrement vocal
        function toggleVoiceRecording() {
            const voiceButton = document.getElementById('voice-button');

            if (!isRecording) {
                startVoiceRecording();
                voiceButton.classList.add('recording');
                voiceButton.innerHTML = '<i class="fas fa-stop"></i>';
                console.log('🎤 Enregistrement vocal démarré');
            } else {
                stopVoiceRecording();
                voiceButton.classList.remove('recording');
                voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                console.log('🎤 Enregistrement vocal arrêté');
            }
        }

        // Démarrer l'enregistrement vocal
        async function startVoiceRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                const audioChunks = [];

                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    processVoiceInput(audioBlob);
                };

                mediaRecorder.start();
                isRecording = true;
            } catch (error) {
                console.error('Erreur accès microphone:', error);
                addMessageToChat('system', '❌ Erreur: Impossible d\'accéder au microphone');
            }
        }

        // Arrêter l'enregistrement vocal
        function stopVoiceRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
        }

        // Traiter l'entrée vocale
        function processVoiceInput(audioBlob) {
            // Simuler la reconnaissance vocale
            addMessageToChat('system', '🎤 Audio reçu - Traitement en cours...');

            setTimeout(() => {
                const simulatedText = "Message vocal simulé";
                document.getElementById('chat-input').value = simulatedText;
                addMessageToChat('system', '✅ Reconnaissance vocale terminée');
            }, 2000);
        }

        // Démarrer la caméra
        async function startCamera() {
            try {
                cameraStream = await navigator.mediaDevices.getUserMedia({ video: true });
                const video = document.getElementById('camera-preview');
                video.srcObject = cameraStream;
                console.log('📷 Caméra démarrée');
            } catch (error) {
                console.error('Erreur accès caméra:', error);
                addMessageToChat('system', '❌ Erreur: Impossible d\'accéder à la caméra');
            }
        }

        // Arrêter la caméra
        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                console.log('📷 Caméra arrêtée');
            }
        }

        // Capturer une image
        function captureImage() {
            const video = document.getElementById('camera-preview');
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            canvas.toBlob(blob => {
                addMessageToChat('system', '📸 Image capturée et envoyée à LOUNA AI');
                console.log('📸 Image capturée');
            });
        }

        // Démarrer le mode automatique
        function startAutoMode() {
            const interval = parseInt(document.getElementById('auto-interval').value) * 1000;
            const statusElement = document.getElementById('auto-status');

            autoInterval = setInterval(() => {
                const autoMessages = [
                    "Comment ça va LOUNA ?",
                    "Peux-tu me donner tes métriques actuelles ?",
                    "Quel est ton état de santé ?",
                    "As-tu des réflexions à partager ?",
                    "Comment évolue ta mémoire thermique ?"
                ];

                const randomMessage = autoMessages[Math.floor(Math.random() * autoMessages.length)];
                document.getElementById('chat-input').value = randomMessage;
                sendMessage();
            }, interval);

            statusElement.textContent = `Mode automatique actif (${interval/1000}s)`;
            statusElement.style.color = '#4ade80';
            console.log(`🤖 Mode automatique démarré (${interval/1000}s)`);
        }

        // Arrêter le mode automatique
        function stopAutoMode() {
            if (autoInterval) {
                clearInterval(autoInterval);
                autoInterval = null;
                document.getElementById('auto-status').textContent = 'Mode automatique désactivé';
                document.getElementById('auto-status').style.color = '#c084fc';
                console.log('🤖 Mode automatique arrêté');
            }
        }

        // ===== FONCTIONS DE SÉCURITÉ =====

        // Connecter la mémoire
        async function connectMemory() {
            try {
                const response = await fetch('/api/memory/connect', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '✅ Mémoire connectée' : '❌ Erreur connexion mémoire');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur: ' + error.message);
            }
        }

        // Déconnecter la mémoire
        async function disconnectMemory() {
            try {
                const response = await fetch('/api/memory/disconnect', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '⚠️ Mémoire déconnectée' : '❌ Erreur déconnexion');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur: ' + error.message);
            }
        }

        // Arrêt d'urgence
        async function emergencyStop() {
            if (confirm('⚠️ ATTENTION: Arrêt d\'urgence du système. Confirmer ?')) {
                try {
                    const response = await fetch('/api/emergency/stop', { method: 'POST' });
                    addMessageToChat('system', '🛑 ARRÊT D\'URGENCE ACTIVÉ');
                    stopAutoMode();
                    stopCamera();
                    stopVoiceRecording();
                } catch (error) {
                    addMessageToChat('system', '❌ Erreur arrêt d\'urgence: ' + error.message);
                }
            }
        }

        // Lancer l'antivirus
        async function runAntivirus() {
            addMessageToChat('system', '🛡️ Scan antivirus en cours...');
            try {
                const response = await fetch('/api/security/antivirus', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '✅ Scan terminé - Système sain' : '⚠️ Menaces détectées');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur antivirus: ' + error.message);
            }
        }

        // Nettoyer la mémoire
        async function cleanMemory() {
            addMessageToChat('system', '🧹 Nettoyage mémoire en cours...');
            try {
                const response = await fetch('/api/memory/clean', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '✅ Mémoire nettoyée' : '❌ Erreur nettoyage');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur nettoyage: ' + error.message);
            }
        }

        // Vérifier le statut
        async function checkStatus() {
            addMessageToChat('system', '📊 Vérification du statut système...');
            try {
                const response = await fetch('/api/system/status');
                const data = await response.json();
                if (data.success) {
                    addMessageToChat('system', `✅ Système opérationnel - CPU: ${data.cpu}% - RAM: ${data.memory}% - Temp: ${data.temperature}°C`);
                } else {
                    addMessageToChat('system', '⚠️ Problèmes détectés dans le système');
                }
            } catch (error) {
                addMessageToChat('system', '❌ Erreur vérification: ' + error.message);
            }
        }

        // Gestion des erreurs globales
        window.addEventListener('error', function(e) {
            console.error('❌ Erreur JavaScript:', e.message, 'à la ligne', e.lineno);
        });

        console.log('📝 Script LOUNA AI Ultra-Révolutionnaire chargé avec toutes les fonctionnalités');
    </script>
</body>
</html>
