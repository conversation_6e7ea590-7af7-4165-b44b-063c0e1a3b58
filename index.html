<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA AI - Intelligence Artificielle Vivante</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-button.active {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }

        .section {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }

        .section.active {
            display: block;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: rgba(255, 107, 107, 0.3);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: rgba(78, 205, 196, 0.3);
            margin-right: auto;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .send-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .interface-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }

        .interface-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .interface-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .brain-viz {
            height: 300px;
            background: radial-gradient(circle, rgba(78, 205, 196, 0.3) 0%, rgba(255, 107, 107, 0.1) 100%);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }

        .neuron {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4ecdc4;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 10px;
            font-size: 12px;
        }

        .status-online {
            color: #4ecdc4;
        }

        .status-offline {
            color: #ff6b6b;
        }

        /* Animations pour le cerveau 3D */
        @keyframes brainPulse {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1) rotate(5deg);
                opacity: 1;
            }
        }

        @keyframes neuronFlash {
            0%, 100% {
                background: #4ecdc4;
                box-shadow: 0 0 5px #4ecdc4;
            }
            50% {
                background: #ff6b6b;
                box-shadow: 0 0 15px #ff6b6b;
            }
        }

        .brain-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .neuron.active {
            animation: neuronFlash 1s infinite;
        }

        .brain-viz canvas {
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <div class="status-indicator">
        <span id="connection-status" class="status-offline">🔴 Déconnecté</span>
    </div>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> LOUNA AI</h1>
            <p>Intelligence Artificielle Vivante de Bureau</p>
        </div>

        <div class="navigation">
            <button class="nav-button active" data-section="dashboard">
                <i class="fas fa-tachometer-alt"></i> Tableau de Bord
            </button>
            <button class="nav-button" data-section="chat">
                <i class="fas fa-comments"></i> Chat IA
            </button>
            <button class="nav-button" data-section="brain">
                <i class="fas fa-brain"></i> Cerveau
            </button>
            <button class="nav-button" data-section="memory">
                <i class="fas fa-memory"></i> Mémoire
            </button>
            <button class="nav-button" data-section="interfaces">
                <i class="fas fa-window-restore"></i> Interfaces
            </button>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="section active">
            <h2><i class="fas fa-chart-line"></i> Métriques en Temps Réel</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="qi-value">100</div>
                    <div class="metric-label">QI Combiné</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="neurons-count">216</div>
                    <div class="metric-label">Neurones Actifs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="temperature">37.0°C</div>
                    <div class="metric-label">Température</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-entries">150</div>
                    <div class="metric-label">Entrées Mémoire</div>
                </div>
            </div>
        </div>

        <!-- Chat Section -->
        <div id="chat" class="section">
            <h2><i class="fas fa-comments"></i> Chat avec LOUNA AI</h2>
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message ai-message">
                        <strong>LOUNA AI:</strong> Bonjour ! Je suis LOUNA AI, votre intelligence artificielle vivante. Comment puis-je vous aider ?
                    </div>
                </div>
                <div class="chat-input-container">
                    <input type="text" class="chat-input" id="chat-input" placeholder="Tapez votre message...">
                    <button class="send-button" id="send-button">
                        <i class="fas fa-paper-plane"></i> Envoyer
                    </button>
                </div>
            </div>
        </div>

        <!-- Brain Section -->
        <div id="brain" class="section">
            <h2><i class="fas fa-brain"></i> Cerveau Artificiel 3D</h2>
            <div class="brain-viz" id="brain-viz">
                <div id="brain-3d-canvas" style="width: 100%; height: 100%; position: relative;">
                    <div id="brain-3d-loading" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #4ecdc4; text-align: center; z-index: 10;">
                        <div style="font-size: 3rem; margin-bottom: 15px; animation: brainPulse 2s ease-in-out infinite;">🧠</div>
                        <div>Initialisation du cerveau 3D...</div>
                    </div>
                </div>
            </div>
            <div class="brain-controls" style="margin-top: 15px; text-align: center;">
                <button class="interface-button" onclick="toggleBrain3DRotation()">🔄 Rotation</button>
                <button class="interface-button" onclick="resetBrain3DView()">🎯 Reset Vue</button>
                <button class="interface-button" onclick="toggleBrain3DNeurons()">⚡ Neurones</button>
            </div>
        </div>

        <!-- Memory Section -->
        <div id="memory" class="section">
            <h2><i class="fas fa-memory"></i> Mémoire Thermique</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="memory-temp">37.0°C</div>
                    <div class="metric-label">Température Mémoire</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-efficiency">99.9%</div>
                    <div class="metric-label">Efficacité</div>
                </div>
            </div>
        </div>

        <!-- Interfaces Section -->
        <div id="interfaces" class="section">
            <h2><i class="fas fa-window-restore"></i> Interfaces Spécialisées</h2>
            <p>Accédez aux différentes interfaces spécialisées de LOUNA AI :</p>
            <div class="interface-buttons">
                <button class="interface-button" onclick="navigateToInterface('/real')">
                    💬 Chat Avancé
                </button>
                <button class="interface-button" onclick="navigateToInterface('/brain-visualization.html')">
                    🧠 Cerveau 3D
                </button>
                <button class="interface-button" onclick="navigateToInterface('/thermal-memory-dashboard.html')">
                    🌡️ Mémoire Thermique
                </button>
                <button class="interface-button" onclick="navigateToInterface('/brain-monitoring-complete.html')">
                    📊 Monitoring Complet
                </button>
                <button class="interface-button" onclick="navigateToInterface('/qi-evolution-test.html')">
                    🧮 Tests QI
                </button>
                <button class="interface-button" onclick="navigateToInterface('/training-interface.html')">
                    🎓 Formation
                </button>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;
        let currentSection = 'dashboard';

        // ========== NAVIGATION ==========
        function showSection(sectionName) {
            console.log('Navigation vers:', sectionName);

            // Masquer toutes les sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            // Afficher la section demandée
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionName;

                // Mettre à jour les boutons de navigation
                document.querySelectorAll('.nav-button').forEach(btn => {
                    btn.classList.remove('active');
                });

                const activeButton = document.querySelector(`[data-section="${sectionName}"]`);
                if (activeButton) {
                    activeButton.classList.add('active');
                }

                // Actions spécifiques par section
                if (sectionName === 'brain') {
                    generateBrainVisualization();
                }
            }
        }

        // ========== CHAT SYSTEM ==========
        async function sendMessage() {
            console.log('🚀 Envoi du message...');

            if (isLoading) {
                console.log('⚠️ Envoi en cours, veuillez patienter');
                return;
            }

            const input = document.getElementById('chat-input');
            const messagesContainer = document.getElementById('chat-messages');

            if (!input || !messagesContainer) {
                console.error('❌ Éléments de chat non trouvés');
                return;
            }

            const message = input.value.trim();
            if (!message) {
                console.log('⚠️ Message vide');
                return;
            }

            isLoading = true;
            input.value = '';

            // Ajouter le message utilisateur
            addMessage(message, 'user');

            try {
                console.log('📡 Envoi vers /api/chat...');
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });

                const data = await response.json();
                console.log('📥 Réponse reçue:', data);

                if (data.success && data.response) {
                    addMessage(data.response, 'ai');
                    if (data.metrics) {
                        updateMetrics(data.metrics);
                    }
                } else {
                    addMessage('Désolé, une erreur est survenue: ' + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                console.error('❌ Erreur chat:', error);
                addMessage('Erreur de connexion au serveur: ' + error.message, 'ai');
            }

            isLoading = false;
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;

            const senderName = sender === 'user' ? 'Vous' : 'LOUNA AI';
            messageDiv.innerHTML = `<strong>${senderName}:</strong> ${text}`;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // ========== METRICS UPDATE ==========
        async function updateMetrics(data = null) {
            try {
                if (!data) {
                    const response = await fetch('/api/metrics');
                    data = await response.json();
                }

                if (data.success || data.brainStats) {
                    // QI
                    let qiValue = '100';
                    if (data.qi) {
                        if (typeof data.qi === 'object') {
                            qiValue = data.qi.combinedIQ || data.qi.combined || data.qi.agentIQ || '100';
                        } else {
                            qiValue = data.qi;
                        }
                    }
                    document.getElementById('qi-value').textContent = qiValue;

                    // Neurones
                    const neurons = data.brainStats?.activeNeurons || data.neurons || '216';
                    document.getElementById('neurons-count').textContent = neurons;

                    // Température
                    const temp = data.thermalStats?.temperature || data.temperature || '37.0';
                    document.getElementById('temperature').textContent = temp + '°C';
                    document.getElementById('memory-temp').textContent = temp + '°C';

                    // Mémoire
                    const memories = data.thermalStats?.totalEntries || data.memoryEntries || '150';
                    document.getElementById('memory-entries').textContent = memories;

                    // Efficacité
                    const efficiency = data.thermalStats?.memoryEfficiency || '99.9';
                    document.getElementById('memory-efficiency').textContent = efficiency + '%';

                    console.log('✅ Métriques mises à jour');
                }
            } catch (error) {
                console.error('❌ Erreur mise à jour métriques:', error);
            }
        }

        // ========== BRAIN VISUALIZATION ==========
        function generateBrainVisualization() {
            console.log('🧠 Génération de la visualisation du cerveau...');

            // Initialiser le cerveau 3D
            setTimeout(() => {
                initBrain3D();
            }, 100);

            // Ajouter quelques neurones 2D en fallback
            const brainViz = document.getElementById('brain-viz');
            if (!brainViz) return;

            // Ne pas effacer le contenu car il contient le canvas 3D
            const existingNeurons = brainViz.querySelectorAll('.neuron');
            existingNeurons.forEach(n => n.remove());

            // Générer des neurones 2D en arrière-plan
            for (let i = 0; i < 15; i++) {
                const neuron = document.createElement('div');
                neuron.className = 'neuron';
                neuron.style.left = Math.random() * 95 + '%';
                neuron.style.top = Math.random() * 95 + '%';
                neuron.style.animationDelay = Math.random() * 2 + 's';
                neuron.style.zIndex = '1';
                brainViz.appendChild(neuron);
            }
        }

        // ========== NAVIGATION TO OTHER INTERFACES ==========
        function navigateToInterface(path) {
            console.log('🔄 Navigation vers:', path);
            addMessage('🔄 Navigation vers ' + path + '...', 'ai');
            window.location.href = path;
        }

        // ========== CONNECTION STATUS ==========
        async function checkConnection() {
            try {
                const response = await fetch('/api/metrics');
                if (response.ok) {
                    document.getElementById('connection-status').innerHTML = '🟢 Connecté';
                    document.getElementById('connection-status').className = 'status-online';
                } else {
                    throw new Error('Réponse non OK');
                }
            } catch (error) {
                document.getElementById('connection-status').innerHTML = '🔴 Déconnecté';
                document.getElementById('connection-status').className = 'status-offline';
            }
        }

        // ========== EVENT LISTENERS ==========
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initialisation de LOUNA AI...');

            // Navigation buttons
            document.querySelectorAll('.nav-button').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    showSection(section);
                });
            });

            // Send button
            const sendButton = document.getElementById('send-button');
            if (sendButton) {
                sendButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('🖱️ Clic sur le bouton d\'envoi');
                    sendMessage();
                });
            }

            // Chat input
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        console.log('⌨️ Touche Entrée pressée');
                        sendMessage();
                    }
                });
            }

            // Initialisation
            generateBrainVisualization();
            updateMetrics();
            checkConnection();

            // Mises à jour périodiques
            setInterval(updateMetrics, 5000);
            setInterval(checkConnection, 10000);

            console.log('✅ LOUNA AI initialisé avec succès');
        });

        // ========== CERVEAU 3D AVANCÉ ==========
        let brain3DScene, brain3DCamera, brain3DRenderer, brain3DNeurons = [], brain3DAnimationId;
        let brain3DActive = false, brain3DRotating = true;

        function initBrain3D() {
            console.log('🧠 Initialisation du cerveau 3D...');

            const container = document.getElementById('brain-3d-canvas');
            const loading = document.getElementById('brain-3d-loading');

            if (!container) {
                console.error('❌ Container brain-3d-canvas non trouvé');
                return;
            }

            // Essayer d'utiliser Three.js si disponible
            if (typeof THREE !== 'undefined') {
                initThreeJSBrain3D(container, loading);
            } else {
                // Charger Three.js dynamiquement
                loadThreeJS().then(() => {
                    initThreeJSBrain3D(container, loading);
                }).catch(() => {
                    initCanvasBrain3D(container, loading);
                });
            }
        }

        function loadThreeJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        function initThreeJSBrain3D(container, loading) {
            try {
                console.log('🚀 Initialisation Three.js...');

                // Masquer le loading
                if (loading) loading.style.display = 'none';

                // Configuration Three.js
                brain3DScene = new THREE.Scene();
                brain3DScene.background = new THREE.Color(0x000011);

                const width = container.offsetWidth || 600;
                const height = container.offsetHeight || 300;

                brain3DCamera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
                brain3DCamera.position.z = 30;

                brain3DRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                brain3DRenderer.setSize(width, height);
                brain3DRenderer.setClearColor(0x000011, 0.8);
                container.appendChild(brain3DRenderer.domElement);

                // Éclairage
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                brain3DScene.add(ambientLight);

                const pointLight = new THREE.PointLight(0x4ecdc4, 1, 100);
                pointLight.position.set(10, 10, 10);
                brain3DScene.add(pointLight);

                const pointLight2 = new THREE.PointLight(0xff6b6b, 0.8, 100);
                pointLight2.position.set(-10, -10, 10);
                brain3DScene.add(pointLight2);

                // Créer le cerveau principal
                const brainGeometry = new THREE.SphereGeometry(8, 32, 32);
                const brainMaterial = new THREE.MeshPhongMaterial({
                    color: 0x2a2a2a,
                    transparent: true,
                    opacity: 0.3,
                    wireframe: true
                });
                const brainMesh = new THREE.Mesh(brainGeometry, brainMaterial);
                brain3DScene.add(brainMesh);

                // Créer des neurones
                createBrain3DNeurons();

                // Animation
                function animate3D() {
                    if (!brain3DActive) return;

                    // Rotation du cerveau
                    if (brain3DRotating) {
                        brainMesh.rotation.y += 0.01;
                        brainMesh.rotation.x += 0.005;
                    }

                    // Animation des neurones
                    brain3DNeurons.forEach((neuron, index) => {
                        const time = Date.now() * 0.001;
                        neuron.material.opacity = 0.5 + 0.5 * Math.sin(time * 2 + index);
                        neuron.scale.setScalar(0.8 + 0.4 * Math.sin(time * 3 + index));
                    });

                    brain3DRenderer.render(brain3DScene, brain3DCamera);
                    brain3DAnimationId = requestAnimationFrame(animate3D);
                }

                brain3DActive = true;
                animate3D();
                console.log('✅ Cerveau 3D Three.js initialisé !');

            } catch (error) {
                console.error('❌ Erreur Three.js:', error);
                initCanvasBrain3D(container, loading);
            }
        }

        function createBrain3DNeurons() {
            const neuronGeometry = new THREE.SphereGeometry(0.3, 8, 8);

            for (let i = 0; i < 50; i++) {
                const neuronMaterial = new THREE.MeshPhongMaterial({
                    color: Math.random() > 0.5 ? 0x4ecdc4 : 0xff6b6b,
                    transparent: true,
                    opacity: 0.8
                });

                const neuron = new THREE.Mesh(neuronGeometry, neuronMaterial);

                // Position aléatoire dans une sphère
                const radius = 6 + Math.random() * 4;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.random() * Math.PI;

                neuron.position.x = radius * Math.sin(phi) * Math.cos(theta);
                neuron.position.y = radius * Math.sin(phi) * Math.sin(theta);
                neuron.position.z = radius * Math.cos(phi);

                brain3DScene.add(neuron);
                brain3DNeurons.push(neuron);
            }
        }

        function initCanvasBrain3D(container, loading) {
            console.log('🎨 Fallback Canvas 2D...');

            if (loading) loading.style.display = 'none';

            const canvas = document.createElement('canvas');
            canvas.width = container.offsetWidth || 600;
            canvas.height = container.offsetHeight || 300;
            canvas.style.borderRadius = '15px';
            container.appendChild(canvas);

            const ctx = canvas.getContext('2d');

            function animateCanvas() {
                if (!brain3DActive) return;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Fond dégradé
                const gradient = ctx.createRadialGradient(
                    canvas.width/2, canvas.height/2, 0,
                    canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)/2
                );
                gradient.addColorStop(0, 'rgba(78, 205, 196, 0.1)');
                gradient.addColorStop(1, 'rgba(0, 0, 17, 0.8)');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Dessiner des neurones animés
                const time = Date.now() * 0.001;
                for (let i = 0; i < 30; i++) {
                    const x = canvas.width/2 + Math.cos(time + i) * (100 + i * 3);
                    const y = canvas.height/2 + Math.sin(time * 1.5 + i) * (50 + i * 2);
                    const size = 3 + 2 * Math.sin(time * 2 + i);
                    const opacity = 0.5 + 0.5 * Math.sin(time * 3 + i);

                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(${i % 2 ? '78, 205, 196' : '255, 107, 107'}, ${opacity})`;
                    ctx.fill();

                    // Connexions
                    if (i > 0) {
                        const prevX = canvas.width/2 + Math.cos(time + i - 1) * (100 + (i-1) * 3);
                        const prevY = canvas.height/2 + Math.sin(time * 1.5 + i - 1) * (50 + (i-1) * 2);

                        ctx.beginPath();
                        ctx.moveTo(x, y);
                        ctx.lineTo(prevX, prevY);
                        ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.3})`;
                        ctx.lineWidth = 1;
                        ctx.stroke();
                    }
                }

                requestAnimationFrame(animateCanvas);
            }

            brain3DActive = true;
            animateCanvas();
            console.log('✅ Cerveau Canvas 2D initialisé !');
        }

        // Contrôles du cerveau 3D
        function toggleBrain3DRotation() {
            brain3DRotating = !brain3DRotating;
            console.log('🔄 Rotation cerveau 3D:', brain3DRotating ? 'ON' : 'OFF');
        }

        function resetBrain3DView() {
            if (brain3DCamera) {
                brain3DCamera.position.set(0, 0, 30);
                brain3DCamera.rotation.set(0, 0, 0);
                console.log('🎯 Vue cerveau 3D réinitialisée');
            }
        }

        function toggleBrain3DNeurons() {
            brain3DNeurons.forEach(neuron => {
                neuron.visible = !neuron.visible;
            });
            console.log('⚡ Neurones 3D:', brain3DNeurons[0]?.visible ? 'VISIBLES' : 'CACHÉS');
        }
    </script>
</body>
</html>
