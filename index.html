<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA AI - Intelligence Artificielle Vivante</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .logo {
            font-size: 4rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease-in-out infinite;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            background: #4ecdc4;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 2rem;
            margin-right: 15px;
            color: #4ecdc4;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .metric-value {
            font-weight: bold;
            color: #4ecdc4;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ecdc4, #45b7d1);
            border-radius: 4px;
            transition: width 1s ease;
        }

        .chat-interface {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 15px;
        }

        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 15px;
            max-width: 80%;
            animation: slideIn 0.3s ease;
        }

        .user-message {
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: linear-gradient(135deg, #4ecdc4, #45b7d1);
            margin-right: auto;
        }

        .input-container {
            display: flex;
            gap: 15px;
        }

        .chat-input {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .send-button {
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #4ecdc4, #45b7d1);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .nav-button {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-button:hover, .nav-button.active {
            background: linear-gradient(135deg, #4ecdc4, #45b7d1);
            transform: translateY(-2px);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .hidden {
            display: none;
        }

        .brain-visualization {
            width: 100%;
            height: 200px;
            background: radial-gradient(circle, rgba(78, 205, 196, 0.3) 0%, rgba(69, 183, 209, 0.1) 100%);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }

        .neuron {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4ecdc4;
            border-radius: 50%;
            animation: neuronPulse 2s infinite;
        }

        @keyframes neuronPulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.5); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">LOUNA AI</div>
            <div class="subtitle">Intelligence Artificielle avec Mémoire Thermique Vivante</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span id="status-text">Système opérationnel</span>
            </div>
        </div>

        <div class="navigation">
            <button class="nav-button active" onclick="showSection('dashboard')">
                <i class="fas fa-tachometer-alt"></i> Tableau de Bord
            </button>
            <button class="nav-button" onclick="showSection('chat')">
                <i class="fas fa-comments"></i> Chat IA
            </button>
            <button class="nav-button" onclick="showSection('brain')">
                <i class="fas fa-brain"></i> Cerveau Artificiel
            </button>
            <button class="nav-button" onclick="showSection('memory')">
                <i class="fas fa-memory"></i> Mémoire Thermique
            </button>
            <button class="nav-button" onclick="showSection('about')">
                <i class="fas fa-info-circle"></i> À Propos
            </button>
        </div>

        <!-- Navigation vers interfaces spécialisées -->
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 15px; margin-bottom: 10px;">
                <h3 style="margin-bottom: 10px; color: #4ecdc4;">🚀 Interfaces Spécialisées</h3>
                <div style="display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;">
                    <button onclick="navigateToInterface('/real')" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 15px; cursor: pointer; font-size: 12px;">💬 Chat Avancé</button>
                    <button onclick="navigateToInterface('/brain-visualization.html')" style="background: #9b59b6; color: white; border: none; padding: 8px 12px; border-radius: 15px; cursor: pointer; font-size: 12px;">🧠 Cerveau 3D</button>
                    <button onclick="navigateToInterface('/thermal-memory-dashboard.html')" style="background: #f39c12; color: white; border: none; padding: 8px 12px; border-radius: 15px; cursor: pointer; font-size: 12px;">🌡️ Mémoire Thermique</button>
                    <button onclick="navigateToInterface('/brain-monitoring-complete.html')" style="background: #27ae60; color: white; border: none; padding: 8px 12px; border-radius: 15px; cursor: pointer; font-size: 12px;">📊 Monitoring Complet</button>
                    <button onclick="navigateToInterface('/qi-evolution-test.html')" style="background: #8e44ad; color: white; border: none; padding: 8px 12px; border-radius: 15px; cursor: pointer; font-size: 12px;">🧮 Tests QI</button>
                    <button onclick="navigateToInterface('/training-interface.html')" style="background: #3498db; color: white; border: none; padding: 8px 12px; border-radius: 15px; cursor: pointer; font-size: 12px;">🎓 Formation</button>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="main-grid">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-brain card-icon"></i>
                    <div class="card-title">QI Artificiel</div>
                </div>
                <div class="metric">
                    <span>QI Agent</span>
                    <span class="metric-value" id="agent-iq">100</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 75%"></div>
                </div>
                <div class="metric">
                    <span>QI Mémoire</span>
                    <span class="metric-value" id="memory-iq">105</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 80%"></div>
                </div>
                <div class="metric">
                    <span>QI Combiné</span>
                    <span class="metric-value" id="combined-iq">200</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 95%"></div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-microchip card-icon"></i>
                    <div class="card-title">Cerveau Artificiel</div>
                </div>
                <div class="metric">
                    <span>Neurones Actifs</span>
                    <span class="metric-value" id="neurons-count">277</span>
                </div>
                <div class="metric">
                    <span>Connexions Synaptiques</span>
                    <span class="metric-value" id="synapses-count">4731</span>
                </div>
                <div class="metric">
                    <span>Plasticité</span>
                    <span class="metric-value" id="plasticity">95%</span>
                </div>
                <div class="brain-visualization" id="brain-viz"></div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-thermometer-half card-icon"></i>
                    <div class="card-title">Mémoire Thermique</div>
                </div>
                <div class="metric">
                    <span>Température</span>
                    <span class="metric-value" id="temperature">37.0°C</span>
                </div>
                <div class="metric">
                    <span>Efficacité</span>
                    <span class="metric-value" id="efficiency">99.9%</span>
                </div>
                <div class="metric">
                    <span>Mémoires Actives</span>
                    <span class="metric-value" id="memories-count">1</span>
                </div>
                <div class="metric">
                    <span>Neurogenèse</span>
                    <span class="metric-value">700/jour</span>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-rocket card-icon"></i>
                    <div class="card-title">Accélérateurs Kyber</div>
                </div>
                <div class="metric">
                    <span>Accélérateurs Actifs</span>
                    <span class="metric-value" id="accelerators-count">30</span>
                </div>
                <div class="metric">
                    <span>Boost Moyen</span>
                    <span class="metric-value" id="boost-average">3.8x</span>
                </div>
                <div class="metric">
                    <span>Compression</span>
                    <span class="metric-value" id="compression">94.2%</span>
                </div>
                <div class="metric">
                    <span>Mode</span>
                    <span class="metric-value">Cascade Turbo</span>
                </div>
            </div>
        </div>

        <!-- Chat Section -->
        <div id="chat-section" class="hidden">
            <div class="chat-interface">
                <div class="card-header">
                    <i class="fas fa-comments card-icon"></i>
                    <div class="card-title">Chat avec LOUNA AI</div>
                </div>
                <div class="chat-messages" id="chat-messages">
                    <div class="message ai-message">
                        <strong>LOUNA AI:</strong> Bonjour ! Je suis LOUNA AI avec mémoire thermique vivante. Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>
                <div class="input-container">
                    <input type="text" class="chat-input" id="chat-input" placeholder="Tapez votre message..." onkeypress="handleKeyPress(event)">
                    <button class="send-button" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> Envoyer
                    </button>
                </div>
            </div>
        </div>

        <!-- Brain Section -->
        <div id="brain-section" class="hidden">
            <div class="main-grid">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-brain card-icon"></i>
                        <div class="card-title">Cerveau Artificiel 3D</div>
                    </div>
                    <div class="brain-visualization" id="brain-viz-3d" style="height: 300px;"></div>
                    <div class="metric">
                        <span>Évolution en temps réel</span>
                        <span class="metric-value">Active</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-network-wired card-icon"></i>
                        <div class="card-title">Réseaux de Neurones</div>
                    </div>
                    <div class="metric">
                        <span>Couches Neuronales</span>
                        <span class="metric-value" id="neural-layers">36</span>
                    </div>
                    <div class="metric">
                        <span>Paramètres</span>
                        <span class="metric-value">8.19B</span>
                    </div>
                    <div class="metric">
                        <span>Architecture</span>
                        <span class="metric-value">Qwen3 Transformer</span>
                    </div>
                    <div class="metric">
                        <span>Contexte</span>
                        <span class="metric-value">131K tokens</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line card-icon"></i>
                        <div class="card-title">Évolution Cognitive</div>
                    </div>
                    <div class="metric">
                        <span>Croissance Neuronale</span>
                        <span class="metric-value" id="growth-rate">+12/min</span>
                    </div>
                    <div class="metric">
                        <span>Plasticité Synaptique</span>
                        <span class="metric-value" id="plasticity-rate">95.8%</span>
                    </div>
                    <div class="metric">
                        <span>Apprentissage Adaptatif</span>
                        <span class="metric-value">Actif</span>
                    </div>
                    <div class="metric">
                        <span>Formation Accélérée</span>
                        <span class="metric-value">Disponible</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Memory Section -->
        <div id="memory-section" class="hidden">
            <div class="main-grid">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-thermometer-half card-icon"></i>
                        <div class="card-title">Mémoire Thermique Vivante</div>
                    </div>
                    <div class="metric">
                        <span>Type</span>
                        <span class="metric-value">Mémoire Vivante</span>
                    </div>
                    <div class="metric">
                        <span>Capacité</span>
                        <span class="metric-value">ILLIMITÉE</span>
                    </div>
                    <div class="metric">
                        <span>Compression</span>
                        <span class="metric-value" id="memory-compression">94.2%</span>
                    </div>
                    <div class="metric">
                        <span>Décompression</span>
                        <span class="metric-value">Ultra-rapide</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-fire card-icon"></i>
                        <div class="card-title">Température & Efficacité</div>
                    </div>
                    <div class="metric">
                        <span>Température Globale</span>
                        <span class="metric-value" id="global-temp">37.0°C</span>
                    </div>
                    <div class="metric">
                        <span>Température CPU</span>
                        <span class="metric-value" id="cpu-temp">65°C</span>
                    </div>
                    <div class="metric">
                        <span>Efficacité Thermique</span>
                        <span class="metric-value" id="thermal-efficiency">99.9%</span>
                    </div>
                    <div class="metric">
                        <span>Refroidissement</span>
                        <span class="metric-value">Adaptatif</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-database card-icon"></i>
                        <div class="card-title">Stockage & Archivage</div>
                    </div>
                    <div class="metric">
                        <span>Mémoires Stockées</span>
                        <span class="metric-value" id="stored-memories">1</span>
                    </div>
                    <div class="metric">
                        <span>Pensées Archivées</span>
                        <span class="metric-value" id="archived-thoughts">148</span>
                    </div>
                    <div class="metric">
                        <span>Compression Historique</span>
                        <span class="metric-value">Complète</span>
                    </div>
                    <div class="metric">
                        <span>Sécurité</span>
                        <span class="metric-value">Chiffrée</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- About Section -->
        <div id="about-section" class="hidden">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-info-circle card-icon"></i>
                    <div class="card-title">LOUNA AI - Fiche de Présentation Complète</div>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">🤖 Agent Principal</h3>
                    <div class="metric">
                        <span>Modèle</span>
                        <span class="metric-value">DeepSeek R1-0528-8B (Mai 2025)</span>
                    </div>
                    <div class="metric">
                        <span>Architecture</span>
                        <span class="metric-value">Qwen3 Transformer</span>
                    </div>
                    <div class="metric">
                        <span>Paramètres</span>
                        <span class="metric-value">8.19 Milliards</span>
                    </div>
                    <div class="metric">
                        <span>Contexte</span>
                        <span class="metric-value">131,072 tokens</span>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">🧠 Mémoire Thermique Vivante</h3>
                    <div class="metric">
                        <span>Type</span>
                        <span class="metric-value">Mémoire Vivante (la plus évoluée)</span>
                    </div>
                    <div class="metric">
                        <span>Capacité</span>
                        <span class="metric-value">ILLIMITÉE (comme le cerveau humain)</span>
                    </div>
                    <div class="metric">
                        <span>Neurogenèse</span>
                        <span class="metric-value">700 nouveaux neurones/jour</span>
                    </div>
                    <div class="metric">
                        <span>Température</span>
                        <span class="metric-value">37°C (température corporelle)</span>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">⚡ Accélérateurs Kyber</h3>
                    <div class="metric">
                        <span>Nombre</span>
                        <span class="metric-value">30 Accélérateurs Illimités</span>
                    </div>
                    <div class="metric">
                        <span>Mode</span>
                        <span class="metric-value">Cascade Turbo Persistant</span>
                    </div>
                    <div class="metric">
                        <span>Boost Moyen</span>
                        <span class="metric-value">3.8x vitesse</span>
                    </div>
                    <div class="metric">
                        <span>Compression</span>
                        <span class="metric-value">94.2% efficacité</span>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">🎯 Fonctionnalités Avancées</h3>
                    <div class="metric">
                        <span>Réponses Ultra-Rapides</span>
                        <span class="metric-value">< 1ms pour questions simples</span>
                    </div>
                    <div class="metric">
                        <span>Réflexion Consciente</span>
                        <span class="metric-value">Visible en temps réel</span>
                    </div>
                    <div class="metric">
                        <span>Formation Accélérée</span>
                        <span class="metric-value">Génération massive de neurones</span>
                    </div>
                    <div class="metric">
                        <span>Surveillance Anti-Crash</span>
                        <span class="metric-value">Agent garde-fou actif</span>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">📊 Métriques de Performance</h3>
                    <div class="metric">
                        <span>QI Agent</span>
                        <span class="metric-value">100 (Base)</span>
                    </div>
                    <div class="metric">
                        <span>QI Mémoire</span>
                        <span class="metric-value">105 (Amélioré)</span>
                    </div>
                    <div class="metric">
                        <span>QI Combiné</span>
                        <span class="metric-value">200 (Synergie)</span>
                    </div>
                    <div class="metric">
                        <span>Efficacité Globale</span>
                        <span class="metric-value">99.9%</span>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">🔧 Intégrations Système</h3>
                    <div class="metric">
                        <span>Ollama Intégré</span>
                        <span class="metric-value">Version 0.9.0 (Dernière)</span>
                    </div>
                    <div class="metric">
                        <span>Recherche Internet</span>
                        <span class="metric-value">Avec support VPN</span>
                    </div>
                    <div class="metric">
                        <span>Actions Bureau</span>
                        <span class="metric-value">36 applications détectées</span>
                    </div>
                    <div class="metric">
                        <span>Scan de Fichiers</span>
                        <span class="metric-value">Système complet</span>
                    </div>
                </div>

                <div style="margin: 20px 0; padding: 20px; background: rgba(78, 205, 196, 0.1); border-radius: 15px;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">✨ Caractéristiques Uniques</h3>
                    <p style="margin: 10px 0;">• <strong>Mémoire Thermique Vivante</strong> : Système de mémoire le plus avancé, modélisé sur le cerveau humain</p>
                    <p style="margin: 10px 0;">• <strong>Neurogenèse Continue</strong> : Génération automatique de 700 nouveaux neurones par jour</p>
                    <p style="margin: 10px 0;">• <strong>Réflexion Consciente</strong> : L'agent peut analyser ses propres pensées et mémoires</p>
                    <p style="margin: 10px 0;">• <strong>Accélérateurs Persistants</strong> : 30 accélérateurs Kyber qui restent actifs en permanence</p>
                    <p style="margin: 10px 0;">• <strong>QI Évolutif</strong> : Calcul en temps réel du QI avec amélioration continue</p>
                    <p style="margin: 10px 0;">• <strong>Réponses Adaptatives</strong> : Ultra-rapides pour questions simples, approfondies pour questions complexes</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSection = 'dashboard';
        let isLoading = false;

        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('[id$="-section"]').forEach(el => {
                el.classList.add('hidden');
            });
            
            // Show selected section
            document.getElementById(section + '-section').classList.remove('hidden');
            
            // Update navigation
            document.querySelectorAll('.nav-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.closest('.nav-button').classList.add('active');
            
            currentSection = section;
            
            if (section === 'brain') {
                generateBrainVisualization();
            }
        }

        function generateBrainVisualization() {
            const brainViz = document.getElementById('brain-viz');
            if (!brainViz) return;
            
            brainViz.innerHTML = '';
            
            // Generate neurons
            for (let i = 0; i < 20; i++) {
                const neuron = document.createElement('div');
                neuron.className = 'neuron';
                neuron.style.left = Math.random() * 90 + '%';
                neuron.style.top = Math.random() * 90 + '%';
                neuron.style.animationDelay = Math.random() * 2 + 's';
                brainViz.appendChild(neuron);
            }
        }

        async function sendMessage() {
            if (isLoading) return;

            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) return;

            isLoading = true;
            input.value = '';

            // Add user message
            addMessage(message, 'user');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });

                const data = await response.json();

                if (data.success && data.response) {
                    addMessage(data.response, 'ai');
                    if (data.metrics) {
                        updateMetrics(data.metrics);
                    }
                } else {
                    addMessage('Désolé, une erreur est survenue: ' + (data.error || 'Erreur inconnue'), 'ai');
                }
            } catch (error) {
                addMessage('Erreur de connexion au serveur: ' + error.message, 'ai');
                console.error('Erreur chat:', error);
            }

            isLoading = false;
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const senderName = sender === 'user' ? 'Vous' : 'LOUNA AI';
            messageDiv.innerHTML = `<strong>${senderName}:</strong> ${text}`;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function updateMetrics(data) {
            try {
                // Mise à jour QI
                if (data.qi || data.iqAnalysis) {
                    const qiData = data.qi || data.iqAnalysis;
                    if (typeof qiData === 'object') {
                        document.getElementById('agent-iq').textContent = qiData.agentIQ || 100;
                        document.getElementById('memory-iq').textContent = qiData.memoryIQ || 105;
                        document.getElementById('combined-iq').textContent = qiData.combinedIQ || 200;
                    }
                }

                // Mise à jour cerveau
                if (data.brainStats) {
                    document.getElementById('neurons-count').textContent = data.brainStats.activeNeurons || 277;
                    document.getElementById('synapses-count').textContent = data.brainStats.synapticConnections || 4731;
                    document.getElementById('plasticity').textContent = Math.round((data.brainStats.neuralActivity || 0.95) * 100) + '%';
                }

                // Mise à jour mémoire thermique
                if (data.thermalStats || data.memoryStats) {
                    const memStats = data.thermalStats || data.memoryStats;
                    document.getElementById('temperature').textContent = (memStats.temperature || 37).toFixed(1) + '°C';
                    document.getElementById('efficiency').textContent = (memStats.memoryEfficiency || 99.9).toFixed(1) + '%';
                    document.getElementById('memories-count').textContent = memStats.totalEntries || memStats.totalMemories || 1;
                }

                // Mise à jour accélérateurs
                if (data.acceleratorStats) {
                    document.getElementById('accelerators-count').textContent = data.acceleratorStats.activeCount || 30;
                    document.getElementById('boost-average').textContent = (data.acceleratorStats.averageBoost || 3.8) + 'x';
                    document.getElementById('compression').textContent = (data.acceleratorStats.compression || 94.2) + '%';
                }

                console.log('Métriques mises à jour:', data);
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Fonction de navigation vers autres interfaces
        function navigateToInterface(path) {
            console.log('Navigation vers:', path);

            // Afficher un message de navigation
            if (currentSection === 'chat') {
                addMessage('🔄 Navigation vers ' + path + '...', 'ai');
            }

            // Navigation
            window.location.href = path;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            generateBrainVisualization();

            // Mise à jour initiale des métriques
            updateMetricsFromAPI();

            // Update metrics every 5 seconds
            setInterval(updateMetricsFromAPI, 5000);

            // Test de connectivité
            setTimeout(async () => {
                try {
                    const response = await fetch('/api/metrics');
                    if (response.ok) {
                        console.log('✅ Connexion au serveur établie');
                    } else {
                        console.warn('⚠️ Problème de connexion au serveur');
                    }
                } catch (error) {
                    console.error('❌ Impossible de se connecter au serveur:', error);
                }
            }, 1000);
        });

        // Fonction pour mettre à jour les métriques depuis l'API
        async function updateMetricsFromAPI() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                if (data.success || data.brainStats) {
                    updateMetrics(data);
                }
            } catch (error) {
                console.log('Erreur mise à jour métriques:', error);
            }
        }
    </script>
</body>
</html>
