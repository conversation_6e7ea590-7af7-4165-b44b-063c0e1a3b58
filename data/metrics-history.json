{"timestamp": 1749467648124, "metrics": {"system": {"cpu": {"usage": 0.0068, "temperature": 37, "cores": 10}, "memory": {"used": 37255432, "total": 17179869184, "efficiency": 8.051309612565703}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749449348022}, "performance": {"uptime": 18298.893, "efficiency": 64.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749467448828, "cpu": 0.2748, "memory": 0.18934104591608047, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749467450828, "cpu": 0.1321, "memory": 0.1864106860011816, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749467452828, "cpu": 0.09219999999999999, "memory": 0.18810993060469627, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749467454828, "cpu": 0.0574, "memory": 0.1867285929620266, "temperature": 37, "efficiency": 68}, {"timestamp": 1749467456829, "cpu": 0.082, "memory": 0.18864553421735764, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749467458830, "cpu": 0.44470000000000004, "memory": 0.19122627563774586, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749467460831, "cpu": 0.1676, "memory": 0.1908568199723959, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749467462831, "cpu": 0.076, "memory": 0.1878509297966957, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749467464833, "cpu": 0.0761, "memory": 0.19183424301445484, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749467466833, "cpu": 0.1041, "memory": 0.1893413718789816, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749467468835, "cpu": 0.4829, "memory": 0.19148695282638073, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749467470836, "cpu": 0.2329, "memory": 0.18874299712479115, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749467472837, "cpu": 0.0961, "memory": 0.19145184196531773, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749467474837, "cpu": 0.0928, "memory": 0.18961853347718716, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749467476838, "cpu": 0.0672, "memory": 0.1915479078888893, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749467478839, "cpu": 0.41869999999999996, "memory": 0.19302219152450562, "temperature": 37, "efficiency": 67}, {"timestamp": 1749467480839, "cpu": 0.1378, "memory": 0.19097253680229187, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749467482839, "cpu": 0.0765, "memory": 0.19292444922029972, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749467484842, "cpu": 0.0834, "memory": 0.19126944243907928, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749467486843, "cpu": 0.0803, "memory": 0.1943124458193779, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749467488843, "cpu": 0.3825, "memory": 0.19631185568869114, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749467490844, "cpu": 0.22920000000000001, "memory": 0.19353688694536686, "temperature": 37, "efficiency": 67}, {"timestamp": 1749467492845, "cpu": 0.1336, "memory": 0.19705183804035187, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749467494847, "cpu": 0.0668, "memory": 0.19462984055280685, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749467496847, "cpu": 0.0574, "memory": 0.19667232409119606, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749467498848, "cpu": 0.4253, "memory": 0.1933861058205366, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749467500848, "cpu": 0.1982, "memory": 0.19673258066177368, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749467502849, "cpu": 0.0868, "memory": 0.19434327259659767, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749467504850, "cpu": 0.06359999999999999, "memory": 0.19420748576521873, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749467506850, "cpu": 0.7237, "memory": 0.1957214903086424, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749467508852, "cpu": 0.3432, "memory": 0.19936161115765572, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749467510852, "cpu": 0.1674, "memory": 0.19662952981889248, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749467512856, "cpu": 0.05689999999999999, "memory": 0.1990710385143757, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749467514856, "cpu": 0.0352, "memory": 0.1970238983631134, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749467516855, "cpu": 0.06989999999999999, "memory": 0.19881208427250385, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749467518857, "cpu": 0.4057, "memory": 0.2001012209802866, "temperature": 37, "efficiency": 66}, {"timestamp": 1749467520858, "cpu": 0.1823, "memory": 0.19829338416457176, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749467522858, "cpu": 0.048299999999999996, "memory": 0.20089023746550083, "temperature": 37, "efficiency": 66}, {"timestamp": 1749467524860, "cpu": 0.04, "memory": 0.1992763951420784, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749467526861, "cpu": 0.0874, "memory": 0.20164977759122849, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749467528868, "cpu": 0.3296, "memory": 0.19923574291169643, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749467530867, "cpu": 0.1761, "memory": 0.2017649356275797, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749467532868, "cpu": 0.048799999999999996, "memory": 0.1999886240810156, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749467534868, "cpu": 0.0563, "memory": 0.1985698938369751, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749467536867, "cpu": 0.053200000000000004, "memory": 0.20022038370370865, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749467538868, "cpu": 0.18630000000000002, "memory": 0.20180325955152512, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749467540869, "cpu": 0.1564, "memory": 0.200343644246459, "temperature": 37, "efficiency": 66}, {"timestamp": 1749467542869, "cpu": 0.0377, "memory": 0.20245052874088287, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749467544871, "cpu": 0.0362, "memory": 0.20065009593963623, "temperature": 37, "efficiency": 66}, {"timestamp": 1749467546872, "cpu": 0.1091, "memory": 0.2038396429270506, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749467548873, "cpu": 0.4606, "memory": 0.20679272711277008, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749467550874, "cpu": 0.0742, "memory": 0.20530130714178085, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749467552875, "cpu": 0.0519, "memory": 0.20274589769542217, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749467554876, "cpu": 0.0361, "memory": 0.20596724934875965, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467556877, "cpu": 0.0288, "memory": 0.20329346880316734, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749467558876, "cpu": 0.1828, "memory": 0.20462493412196636, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749467560880, "cpu": 0.0644, "memory": 0.203058123588562, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749467562880, "cpu": 0.0343, "memory": 0.20574000664055347, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749467564881, "cpu": 0.0385, "memory": 0.20459149964153767, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749467566883, "cpu": 0.0395, "memory": 0.2060947474092245, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467568883, "cpu": 0.1429, "memory": 0.20893951877951622, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467570898, "cpu": 0.1389, "memory": 0.20703570917248726, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467572887, "cpu": 0.0558, "memory": 0.20908121950924397, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467574886, "cpu": 0.030699999999999998, "memory": 0.20787646062672138, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467576889, "cpu": 0.0413, "memory": 0.20966362208127975, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467578888, "cpu": 0.1495, "memory": 0.2068576868623495, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467580891, "cpu": 0.1333, "memory": 0.20574950613081455, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467582891, "cpu": 0.0303, "memory": 0.20755487494170666, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467584893, "cpu": 0.029799999999999997, "memory": 0.20667491480708122, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467586894, "cpu": 0.0435, "memory": 0.20775855518877506, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467588894, "cpu": 0.0463, "memory": 0.20666830241680145, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467590894, "cpu": 0.1227, "memory": 0.20788954570889473, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467592895, "cpu": 0.0525, "memory": 0.21138829179108143, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467594896, "cpu": 0.0378, "memory": 0.20985277369618416, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749467596897, "cpu": 0.066, "memory": 0.2118505071848631, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467598896, "cpu": 0.0673, "memory": 0.2112146932631731, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467600897, "cpu": 0.1582, "memory": 0.20815557800233364, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749467602898, "cpu": 0.059699999999999996, "memory": 0.2104494720697403, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467604900, "cpu": 0.048, "memory": 0.2095193136483431, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467606900, "cpu": 0.08940000000000001, "memory": 0.21881433203816414, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749467608901, "cpu": 0.07519999999999999, "memory": 0.21046604961156845, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749467610902, "cpu": 0.1437, "memory": 0.21749967709183693, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749467612902, "cpu": 0.0691, "memory": 0.21970453672111034, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749467614904, "cpu": 0.0456, "memory": 0.2112861257046461, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467616904, "cpu": 0.0301, "memory": 0.2126176841557026, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467618907, "cpu": 0.2755, "memory": 0.21133986301720142, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467620907, "cpu": 0.1337, "memory": 0.21244268864393234, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467622908, "cpu": 0.0616, "memory": 0.21545877680182457, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467624909, "cpu": 0.043199999999999995, "memory": 0.2133290283381939, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467626908, "cpu": 0.0535, "memory": 0.2148889470845461, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467628909, "cpu": 0.0275, "memory": 0.21289782598614693, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467630909, "cpu": 0.0826, "memory": 0.21463576704263687, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467632908, "cpu": 0.0558, "memory": 0.21737925708293915, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467634910, "cpu": 0.0726, "memory": 0.21653962321579456, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467636911, "cpu": 0.0706, "memory": 0.21839849650859833, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467638911, "cpu": 0.0485, "memory": 0.21723909303545952, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467640913, "cpu": 0.10579999999999999, "memory": 0.21443148143589497, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467642914, "cpu": 0.036000000000000004, "memory": 0.216298783197999, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467644916, "cpu": 0.0485, "memory": 0.2154004294425249, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467646915, "cpu": 0.0068, "memory": 0.2168551553040743, "temperature": 37, "efficiency": 64.7}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}