{"timestamp": 1749449229687, "metrics": {"system": {"cpu": {"usage": 0.0087, "temperature": 37, "cores": 10}, "memory": {"used": 20757992, "total": 17179869184, "efficiency": 4.523942339393372}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749448629667}, "performance": {"uptime": 598.439, "efficiency": 63.5, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749449030008, "cpu": 0.0143, "memory": 0.0980608630925417, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749449032008, "cpu": 0.0196, "memory": 0.09457073174417019, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749449034009, "cpu": 0.0313, "memory": 0.09863669984042645, "temperature": 37, "efficiency": 68}, {"timestamp": 1749449036009, "cpu": 0.0284, "memory": 0.09659347124397755, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749449038009, "cpu": 0.0164, "memory": 0.09778160601854324, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749449040010, "cpu": 0.0078, "memory": 0.09841634891927242, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749449042012, "cpu": 0.015, "memory": 0.09563462808728218, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749449044014, "cpu": 0.025599999999999998, "memory": 0.09714821353554726, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749449046014, "cpu": 0.015, "memory": 0.0992816872894764, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749449048014, "cpu": 0.012400000000000001, "memory": 0.10133679024875164, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749449050015, "cpu": 0.0118, "memory": 0.09927051141858101, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749449052016, "cpu": 0.006600000000000001, "memory": 0.09957854636013508, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749449054015, "cpu": 0.0117, "memory": 0.10202690027654171, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749449056017, "cpu": 0.0145, "memory": 0.10096672922372818, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749449058018, "cpu": 0.0063999999999999994, "memory": 0.0999759417027235, "temperature": 37, "efficiency": 68}, {"timestamp": 1749449060018, "cpu": 0.006999999999999999, "memory": 0.09824484586715698, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749449062020, "cpu": 0.0137, "memory": 0.10049371048808098, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749449064021, "cpu": 0.0059, "memory": 0.09963861666619778, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749449066021, "cpu": 0.0104, "memory": 0.10100989602506161, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749449068023, "cpu": 0.020900000000000002, "memory": 0.10253828950226307, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749449070024, "cpu": 0.0138, "memory": 0.10379599407315254, "temperature": 37, "efficiency": 67}, {"timestamp": 1749449072025, "cpu": 0.013, "memory": 0.10162936523556709, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749449074026, "cpu": 0.017, "memory": 0.10320586152374744, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749449076027, "cpu": 0.0202, "memory": 0.10020462796092033, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749449078028, "cpu": 0.0151, "memory": 0.10162508115172386, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749449080028, "cpu": 0.029, "memory": 0.10274979285895824, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749449082030, "cpu": 0.0101, "memory": 0.10302304290235043, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749449084031, "cpu": 0.0072, "memory": 0.10563293471932411, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749449086032, "cpu": 0.0176, "memory": 0.10467860847711563, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749449088034, "cpu": 0.0185, "memory": 0.10423948988318443, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749449090034, "cpu": 0.0073999999999999995, "memory": 0.10184748098254204, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749449092035, "cpu": 0.0194, "memory": 0.1050096470862627, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749449094036, "cpu": 0.0095, "memory": 0.1043104100972414, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749449096037, "cpu": 0.0145, "memory": 0.10358039289712906, "temperature": 37, "efficiency": 67}, {"timestamp": 1749449098038, "cpu": 0.0186, "memory": 0.10529118590056896, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749449100039, "cpu": 0.009600000000000001, "memory": 0.10692188516259193, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749449102040, "cpu": 0.0135, "memory": 0.10527130216360092, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749449104041, "cpu": 0.0237, "memory": 0.107250502333045, "temperature": 37, "efficiency": 66}, {"timestamp": 1749449106043, "cpu": 0.0105, "memory": 0.10463190264999866, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749449108043, "cpu": 0.0072, "memory": 0.1063839066773653, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749449110044, "cpu": 0.0113, "memory": 0.10662386193871498, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749449112045, "cpu": 0.0067, "memory": 0.10831165127456188, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749449114046, "cpu": 0.0069, "memory": 0.10549887083470821, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749449116047, "cpu": 0.0095, "memory": 0.10464214719831944, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749449118047, "cpu": 0.0059, "memory": 0.10472121648490429, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749449120048, "cpu": 0.0067, "memory": 0.10903324000537395, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749449122049, "cpu": 0.009600000000000001, "memory": 0.10739210993051529, "temperature": 37, "efficiency": 66}, {"timestamp": 1749449124050, "cpu": 0.067, "memory": 0.10685650631785393, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749449126052, "cpu": 0.0127, "memory": 0.10964949615299702, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749449128052, "cpu": 0.0252, "memory": 0.10827411897480488, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749449130053, "cpu": 0.0085, "memory": 0.10897037573158741, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749449132054, "cpu": 0.0086, "memory": 0.10686037130653858, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749449134063, "cpu": 0.015799999999999998, "memory": 0.10910839773714542, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749449136057, "cpu": 0.0169, "memory": 0.11082054115831852, "temperature": 37, "efficiency": 65}, {"timestamp": 1749449138058, "cpu": 0.0065, "memory": 0.10745683684945107, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749449140059, "cpu": 0.0102, "memory": 0.10956595651805401, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749449142061, "cpu": 0.006600000000000001, "memory": 0.11039408855140209, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749449144062, "cpu": 0.009600000000000001, "memory": 0.10783500038087368, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749449146062, "cpu": 0.15839999999999999, "memory": 0.10866695083677769, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749449148064, "cpu": 0.0119, "memory": 0.10827016085386276, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749449150064, "cpu": 0.0232, "memory": 0.11266395449638367, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749449152066, "cpu": 0.1227, "memory": 0.10867281816899776, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749449154067, "cpu": 0.015200000000000002, "memory": 0.11076405644416809, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749449156068, "cpu": 0.0156, "memory": 0.11038384400308132, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749449158069, "cpu": 0.009600000000000001, "memory": 0.11275559663772583, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749449160070, "cpu": 0.0063999999999999994, "memory": 0.10947217233479023, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749449162071, "cpu": 0.0077, "memory": 0.11392971500754356, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749449164071, "cpu": 0.0107, "memory": 0.112489378079772, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749449166072, "cpu": 0.0143, "memory": 0.1143009401857853, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749449168074, "cpu": 0.0075, "memory": 0.11233077384531498, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749449170075, "cpu": 0.0093, "memory": 0.11403248645365238, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749449172075, "cpu": 0.0078, "memory": 0.11086063459515572, "temperature": 37, "efficiency": 65}, {"timestamp": 1749449174075, "cpu": 0.0063, "memory": 0.1134851947426796, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749449176082, "cpu": 0.0223, "memory": 0.11374717578291893, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749449178083, "cpu": 0.0095, "memory": 0.11363956145942211, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749449180085, "cpu": 0.0060999999999999995, "memory": 0.11263210326433182, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749449182085, "cpu": 0.0095, "memory": 0.11205947957932949, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749449184087, "cpu": 0.0063, "memory": 0.11212634854018688, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749449186087, "cpu": 0.012799999999999999, "memory": 0.11557363905012608, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749449188089, "cpu": 0.0086, "memory": 0.11289059184491634, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749449190090, "cpu": 0.0063999999999999994, "memory": 0.11425060220062733, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749449192090, "cpu": 0.0102, "memory": 0.11326116509735584, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749449194092, "cpu": 0.0136, "memory": 0.11601238511502743, "temperature": 37, "efficiency": 64}, {"timestamp": 1749449196090, "cpu": 0.0103, "memory": 0.1138507854193449, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749449198093, "cpu": 0.0063, "memory": 0.11686510406434536, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749449200094, "cpu": 0.0155, "memory": 0.11419728398323059, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749449202095, "cpu": 0.0081, "memory": 0.11552073992788792, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749449204095, "cpu": 0.0113, "memory": 0.11909808963537216, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749449206097, "cpu": 0.009600000000000001, "memory": 0.115184485912323, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749449208098, "cpu": 0.0077, "memory": 0.11548870243132114, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749449210099, "cpu": 0.0081, "memory": 0.11523514986038208, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749449212100, "cpu": 0.016, "memory": 0.11551096104085445, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749449214101, "cpu": 0.0125, "memory": 0.11559580452740192, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749449216102, "cpu": 0.0084, "memory": 0.11635469272732735, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749449218102, "cpu": 0.0063999999999999994, "memory": 0.11966535821557045, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749449220103, "cpu": 0.0114, "memory": 0.11624856851994991, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749449222104, "cpu": 0.011000000000000001, "memory": 0.11654999107122421, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749449224104, "cpu": 0.0126, "memory": 0.11978605762124062, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749449226105, "cpu": 0.0138, "memory": 0.11740671470761299, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749449228106, "cpu": 0.0087, "memory": 0.12082741595804691, "temperature": 37, "efficiency": 63.5}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}