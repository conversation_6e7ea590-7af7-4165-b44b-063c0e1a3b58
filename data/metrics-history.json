{"timestamp": 1749467348122, "metrics": {"system": {"cpu": {"usage": 0.0135, "temperature": 37, "cores": 10}, "memory": {"used": 36137376, "total": 17179869184, "efficiency": 7.208649952671436}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749449348022}, "performance": {"uptime": 17998.765, "efficiency": 64.4, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749467148631, "cpu": 0.10389999999999999, "memory": 0.20207762718200684, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467150631, "cpu": 0.0453, "memory": 0.2008276991546154, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467152633, "cpu": 0.10089999999999999, "memory": 0.20272433757781982, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467154635, "cpu": 0.0526, "memory": 0.20095719955861568, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467156635, "cpu": 0.0455, "memory": 0.19773994572460651, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749467158637, "cpu": 0.0673, "memory": 0.19875993020832539, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749467160639, "cpu": 0.0776, "memory": 0.20248065702617168, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467162639, "cpu": 0.0462, "memory": 0.19960934296250343, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467164641, "cpu": 0.0895, "memory": 0.20276731811463833, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467166643, "cpu": 0.049600000000000005, "memory": 0.1993396319448948, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467168646, "cpu": 0.0446, "memory": 0.20154770463705063, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467170647, "cpu": 0.4508, "memory": 0.19883178174495697, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467172649, "cpu": 0.038900000000000004, "memory": 0.20151170901954174, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467174651, "cpu": 0.0521, "memory": 0.19933702424168587, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467176651, "cpu": 0.0408, "memory": 0.20084502175450325, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467178654, "cpu": 0.0494, "memory": 0.2023140899837017, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467180657, "cpu": 0.0492, "memory": 0.2004473004490137, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467182657, "cpu": 0.0519, "memory": 0.2018507570028305, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467184661, "cpu": 0.0452, "memory": 0.19976692274212837, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749467186661, "cpu": 0.06899999999999999, "memory": 0.2030364703387022, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749467188665, "cpu": 0.0453, "memory": 0.20454400219023228, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467190664, "cpu": 0.0667, "memory": 0.20219669677317142, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749467192667, "cpu": 0.0324, "memory": 0.2050552051514387, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749467194669, "cpu": 0.0631, "memory": 0.2023742999881506, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467196671, "cpu": 0.015300000000000001, "memory": 0.20428798161447048, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467198674, "cpu": 0.012199999999999999, "memory": 0.2007889561355114, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467200677, "cpu": 0.0167, "memory": 0.2039393875747919, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467202679, "cpu": 0.0109, "memory": 0.20080721005797386, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467204681, "cpu": 0.0131, "memory": 0.2044457010924816, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467206681, "cpu": 0.0135, "memory": 0.20053773187100887, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467208685, "cpu": 0.0134, "memory": 0.20255143754184246, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467210686, "cpu": 0.0139, "memory": 0.20050806924700737, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467212689, "cpu": 0.014799999999999999, "memory": 0.20218812860548496, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467214690, "cpu": 0.0131, "memory": 0.2007775940001011, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467216693, "cpu": 0.012799999999999999, "memory": 0.20266068167984486, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467218693, "cpu": 0.015300000000000001, "memory": 0.20360974594950676, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467220696, "cpu": 0.0157, "memory": 0.20229113288223743, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467222699, "cpu": 0.0165, "memory": 0.20403466187417507, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467224702, "cpu": 0.0195, "memory": 0.20165937021374702, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467226703, "cpu": 0.0114, "memory": 0.20306562073528767, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467228705, "cpu": 0.014799999999999999, "memory": 0.20483192056417465, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467230707, "cpu": 0.015300000000000001, "memory": 0.20173639059066772, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467232711, "cpu": 0.014899999999999998, "memory": 0.2045098226517439, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467234712, "cpu": 0.013200000000000002, "memory": 0.20223930478096008, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467236716, "cpu": 0.013999999999999999, "memory": 0.20358897745609283, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467238715, "cpu": 0.4548, "memory": 0.20494498312473297, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749467240717, "cpu": 0.015300000000000001, "memory": 0.20284880883991718, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749467242721, "cpu": 0.012199999999999999, "memory": 0.20430437289178371, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467244723, "cpu": 0.016, "memory": 0.2022523432970047, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467246724, "cpu": 0.0139, "memory": 0.2036471851170063, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749467248726, "cpu": 0.4433, "memory": 0.2050385344773531, "temperature": 37, "efficiency": 65}, {"timestamp": 1749467250727, "cpu": 0.014100000000000001, "memory": 0.2032068558037281, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467252730, "cpu": 0.015899999999999997, "memory": 0.2055590506643057, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467254730, "cpu": 0.0194, "memory": 0.20336667075753212, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467256733, "cpu": 0.017, "memory": 0.205300934612751, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467258734, "cpu": 0.48760000000000003, "memory": 0.2066014800220728, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467260735, "cpu": 0.0139, "memory": 0.2046321053057909, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467262735, "cpu": 0.013300000000000001, "memory": 0.20661265589296818, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749467264737, "cpu": 0.0162, "memory": 0.2047424204647541, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467266738, "cpu": 0.0143, "memory": 0.20562675781548023, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467268742, "cpu": 0.46249999999999997, "memory": 0.20754504948854446, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749467270743, "cpu": 0.0137, "memory": 0.20519648678600788, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467272745, "cpu": 0.0137, "memory": 0.2071500290185213, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467274746, "cpu": 0.017499999999999998, "memory": 0.2041360829025507, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467276748, "cpu": 0.0138, "memory": 0.20592533983290195, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749467278750, "cpu": 0.4598, "memory": 0.2069016918540001, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749467280752, "cpu": 0.0134, "memory": 0.20533576607704163, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467282754, "cpu": 0.0143, "memory": 0.20715654827654362, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467284756, "cpu": 0.0155, "memory": 0.20477408543229103, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467286757, "cpu": 0.0183, "memory": 0.2061532810330391, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749467288758, "cpu": 0.1603, "memory": 0.20789792761206627, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749467290759, "cpu": 0.0284, "memory": 0.2046877983957529, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467292761, "cpu": 0.0274, "memory": 0.20725694485008717, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467294762, "cpu": 0.0237, "memory": 0.20514517091214657, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467296762, "cpu": 0.0242, "memory": 0.20657437853515148, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749467298762, "cpu": 0.5466000000000001, "memory": 0.20806901156902313, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749467300762, "cpu": 0.0172, "memory": 0.20645223557949066, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749467302765, "cpu": 0.0348, "memory": 0.20820596255362034, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749467304766, "cpu": 0.025300000000000003, "memory": 0.20518507808446884, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467306766, "cpu": 0.0189, "memory": 0.20897099748253822, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749467308768, "cpu": 0.42389999999999994, "memory": 0.21028975024819374, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467310769, "cpu": 0.0229, "memory": 0.2071940340101719, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749467312770, "cpu": 0.022699999999999998, "memory": 0.20943116396665573, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749467314769, "cpu": 0.0233, "memory": 0.2064085565507412, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467316772, "cpu": 0.0226, "memory": 0.20838268101215363, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467318772, "cpu": 0.2878, "memory": 0.20641912706196308, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467320774, "cpu": 0.0162, "memory": 0.2098678145557642, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467322774, "cpu": 0.013200000000000002, "memory": 0.20736143924295902, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749467324775, "cpu": 0.0101, "memory": 0.21134414710104465, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749467326776, "cpu": 0.0229, "memory": 0.20779171027243137, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467328777, "cpu": 0.3764, "memory": 0.21034274250268936, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749467330777, "cpu": 0.017, "memory": 0.20828940905630589, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749467332780, "cpu": 0.0213, "memory": 0.21053371019661427, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749467334779, "cpu": 0.0168, "memory": 0.2096434123814106, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749467336781, "cpu": 0.0137, "memory": 0.2115014474838972, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467338781, "cpu": 0.23310000000000003, "memory": 0.21376702934503555, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749467340783, "cpu": 0.0137, "memory": 0.2116040326654911, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749467342784, "cpu": 0.025599999999999998, "memory": 0.21370179019868374, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749467344786, "cpu": 0.0189, "memory": 0.21292087621986866, "temperature": 37, "efficiency": 64}, {"timestamp": 1749467346787, "cpu": 0.0135, "memory": 0.21034721285104752, "temperature": 37, "efficiency": 64.4}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}