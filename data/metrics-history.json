{"timestamp": 1749470171898, "metrics": {"system": {"cpu": {"usage": 0.0058, "temperature": 37, "cores": 10}, "memory": {"used": 19451816, "total": 17179869184, "efficiency": 15.67864851518111}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749468971932}, "performance": {"uptime": 1198.655, "efficiency": 67.2, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749469972462, "cpu": 0.47809999999999997, "memory": 0.12304093688726425, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469974463, "cpu": 0.0114, "memory": 0.12364634312689304, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469976462, "cpu": 0.0116, "memory": 0.12138821184635162, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469978464, "cpu": 0.7545999999999999, "memory": 0.12292605824768543, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749469980465, "cpu": 0.025300000000000003, "memory": 0.12379763647913933, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749469982467, "cpu": 0.01, "memory": 0.12279152870178223, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469984467, "cpu": 0.3449, "memory": 0.1215081661939621, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749469986469, "cpu": 0.0115, "memory": 0.1250872854143381, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469988469, "cpu": 0.5042, "memory": 0.12408215552568436, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469990470, "cpu": 0.2433, "memory": 0.12285285629332066, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749469992471, "cpu": 0.009899999999999999, "memory": 0.1230953261256218, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469994473, "cpu": 2.3253, "memory": 0.12692897580564022, "temperature": 37, "efficiency": 63}, {"timestamp": 1749469996473, "cpu": 0.42669999999999997, "memory": 0.08088406175374985, "temperature": 37, "efficiency": 75.1}, {"timestamp": 1749469998476, "cpu": 0.48190000000000005, "memory": 0.0810794997960329, "temperature": 37, "efficiency": 75}, {"timestamp": 1749470000475, "cpu": 0.01, "memory": 0.08165589533746243, "temperature": 37, "efficiency": 75.1}, {"timestamp": 1749470002476, "cpu": 0.4733, "memory": 0.08339523337781429, "temperature": 37, "efficiency": 74.5}, {"timestamp": 1749470004477, "cpu": 0.0092, "memory": 0.08458620868623257, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470006480, "cpu": 0.059199999999999996, "memory": 0.08600433357059956, "temperature": 37, "efficiency": 74.9}, {"timestamp": 1749470008480, "cpu": 0.2773, "memory": 0.08723810315132141, "temperature": 37, "efficiency": 74.5}, {"timestamp": 1749470010480, "cpu": 0.0116, "memory": 0.08848346769809723, "temperature": 37, "efficiency": 76}, {"timestamp": 1749470012482, "cpu": 0.0078, "memory": 0.08367900736629963, "temperature": 37, "efficiency": 74.6}, {"timestamp": 1749470014483, "cpu": 0.24719999999999998, "memory": 0.08746674284338951, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470016484, "cpu": 0.0095, "memory": 0.08535068482160568, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749470018487, "cpu": 0.2828, "memory": 0.08466886356472969, "temperature": 37, "efficiency": 74.2}, {"timestamp": 1749470020488, "cpu": 0.38430000000000003, "memory": 0.08809631690382957, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470022488, "cpu": 0.012899999999999998, "memory": 0.0883560162037611, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749470024489, "cpu": 0.0095, "memory": 0.0886793714016676, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470026489, "cpu": 0.2415, "memory": 0.08530211634933949, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749470028489, "cpu": 0.0739, "memory": 0.0853473786264658, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749470030490, "cpu": 0.0336, "memory": 0.09009167551994324, "temperature": 37, "efficiency": 73}, {"timestamp": 1749470032491, "cpu": 0.0088, "memory": 0.08940543048083782, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749470034492, "cpu": 0.0351, "memory": 0.08836002089083195, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749470036492, "cpu": 0.022699999999999998, "memory": 0.08680792525410652, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749470038493, "cpu": 0.23149999999999998, "memory": 0.09090686216950417, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749470040493, "cpu": 0.0060999999999999995, "memory": 0.08981553837656975, "temperature": 37, "efficiency": 73}, {"timestamp": 1749470042494, "cpu": 0.0082, "memory": 0.08834614418447018, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749470044496, "cpu": 0.0078, "memory": 0.08960142731666565, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749470046497, "cpu": 0.0073, "memory": 0.09068762883543968, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749470048497, "cpu": 0.005399999999999999, "memory": 0.09165364317595959, "temperature": 37, "efficiency": 72.6}, {"timestamp": 1749470050497, "cpu": 0.006, "memory": 0.09134435094892979, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749470052497, "cpu": 0.0098, "memory": 0.08915746584534645, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470054499, "cpu": 0.0097, "memory": 0.09051663801074028, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749470056498, "cpu": 0.0113, "memory": 0.08886312134563923, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470058499, "cpu": 0.0051, "memory": 0.08865403942763805, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470060500, "cpu": 0.0042, "memory": 0.09226091206073761, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470062501, "cpu": 0.0060999999999999995, "memory": 0.09194165468215942, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749470064502, "cpu": 0.0052, "memory": 0.09001609869301319, "temperature": 37, "efficiency": 73}, {"timestamp": 1749470066504, "cpu": 0.009399999999999999, "memory": 0.09207529947161674, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470068505, "cpu": 0.0071, "memory": 0.09364420548081398, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749470070506, "cpu": 0.012400000000000001, "memory": 0.09515713900327682, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749470072506, "cpu": 0.007899999999999999, "memory": 0.09421822614967823, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749470074507, "cpu": 0.0065, "memory": 0.09556012228131294, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749470076508, "cpu": 0.009899999999999999, "memory": 0.09179729968309402, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749470078541, "cpu": 0.0082, "memory": 0.09399596601724625, "temperature": 37, "efficiency": 72}, {"timestamp": 1749470080542, "cpu": 0.0055000000000000005, "memory": 0.09309425950050354, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749470082542, "cpu": 0.0059, "memory": 0.09603360667824745, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470084543, "cpu": 0.0068, "memory": 0.09309952147305012, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749470086545, "cpu": 0.0068, "memory": 0.0928100198507309, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749470088546, "cpu": 0.0221, "memory": 0.09747715666890144, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749470090546, "cpu": 0.0109, "memory": 0.09668469429016113, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470092547, "cpu": 0.0083, "memory": 0.09680618532001972, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749470094549, "cpu": 0.0126, "memory": 0.09657912887632847, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749470096549, "cpu": 0.017, "memory": 0.09544515050947666, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749470098551, "cpu": 0.010799999999999999, "memory": 0.09716860949993134, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749470100551, "cpu": 0.014799999999999999, "memory": 0.09823637083172798, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749470102552, "cpu": 0.013200000000000002, "memory": 0.09789015166461468, "temperature": 37, "efficiency": 71}, {"timestamp": 1749470104553, "cpu": 0.017, "memory": 0.09886864572763443, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749470106554, "cpu": 0.0085, "memory": 0.095686549320817, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749470108556, "cpu": 0.0134, "memory": 0.09772335179150105, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749470110556, "cpu": 0.0057, "memory": 0.09616343304514885, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470112557, "cpu": 0.0121, "memory": 0.0993823166936636, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749470114558, "cpu": 0.007600000000000001, "memory": 0.09817606769502163, "temperature": 37, "efficiency": 71}, {"timestamp": 1749470116559, "cpu": 0.0181, "memory": 0.10037217289209366, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749470118560, "cpu": 0.0101, "memory": 0.10092044249176979, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749470120562, "cpu": 0.0104, "memory": 0.10041049681603909, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749470122562, "cpu": 0.0057, "memory": 0.09847227483987808, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749470124564, "cpu": 0.0182, "memory": 0.10242434218525887, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749470126564, "cpu": 0.0073999999999999995, "memory": 0.10441797785460949, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749470128565, "cpu": 0.0055000000000000005, "memory": 0.10218429379165173, "temperature": 37, "efficiency": 73}, {"timestamp": 1749470130571, "cpu": 0.0068, "memory": 0.1030398067086935, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749470132567, "cpu": 0.0111, "memory": 0.10657566599547863, "temperature": 37, "efficiency": 72}, {"timestamp": 1749470134568, "cpu": 0.0071, "memory": 0.11303611099720001, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749470136569, "cpu": 0.01, "memory": 0.11369194835424423, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749470138570, "cpu": 0.0102, "memory": 0.10622101835906506, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749470140571, "cpu": 0.1413, "memory": 0.11027064174413681, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749470142571, "cpu": 0.0098, "memory": 0.10977699421346188, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749470144571, "cpu": 0.012, "memory": 0.10793497785925865, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749470146574, "cpu": 0.0104, "memory": 0.10710121132433414, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749470148573, "cpu": 0.007899999999999999, "memory": 0.10917908512055874, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749470150574, "cpu": 0.0086, "memory": 0.10901829227805138, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749470152576, "cpu": 0.0060999999999999995, "memory": 0.11032391339540482, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749470154578, "cpu": 0.0081, "memory": 0.1120920293033123, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749470156578, "cpu": 0.0119, "memory": 0.11248262599110603, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749470158580, "cpu": 0.006, "memory": 0.11145290918648243, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749470160580, "cpu": 0.0071, "memory": 0.1089245080947876, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749470162582, "cpu": 0.0053, "memory": 0.11374163441359997, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749470164583, "cpu": 0.0057, "memory": 0.1121418084949255, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749470166586, "cpu": 0.0107, "memory": 0.1103302463889122, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749470168586, "cpu": 0.0049, "memory": 0.11437786743044853, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749470170587, "cpu": 0.0058, "memory": 0.11322447098791599, "temperature": 37, "efficiency": 67.2}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}