{"timestamp": 1749451447949, "metrics": {"system": {"cpu": {"usage": 0.0327, "temperature": 37, "cores": 10}, "memory": {"used": 18546808, "total": 17179869184, "efficiency": 9.22154577235365}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749449348022}, "performance": {"uptime": 2099.221, "efficiency": 65.1, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749451249154, "cpu": 0.0106, "memory": 0.09033391252160072, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451251156, "cpu": 0.0113, "memory": 0.08823075331747532, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749451253156, "cpu": 0.0067, "memory": 0.08745645172894001, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749451255158, "cpu": 0.0134, "memory": 0.08951360359787941, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749451257158, "cpu": 0.0084, "memory": 0.08776490576565266, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451259160, "cpu": 0.014100000000000001, "memory": 0.08810521103441715, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451261160, "cpu": 0.0072, "memory": 0.0891245435923338, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451263161, "cpu": 0.009899999999999999, "memory": 0.08883108384907246, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451265161, "cpu": 0.0125, "memory": 0.09185336530208588, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451267163, "cpu": 0.0102, "memory": 0.08860016241669655, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749451269162, "cpu": 0.0045000000000000005, "memory": 0.0907468143850565, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451271164, "cpu": 0.0048000000000000004, "memory": 0.08926829323172569, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451273164, "cpu": 0.008, "memory": 0.0918717123568058, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451275165, "cpu": 0.013300000000000001, "memory": 0.09073861874639988, "temperature": 37, "efficiency": 67}, {"timestamp": 1749451277165, "cpu": 0.0111, "memory": 0.09306054562330246, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749451279166, "cpu": 0.0113, "memory": 0.09170752018690109, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451281167, "cpu": 0.012899999999999998, "memory": 0.09427969343960285, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451283168, "cpu": 0.025, "memory": 0.09398991242051125, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749451285169, "cpu": 0.0339, "memory": 0.09308848530054092, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451287170, "cpu": 0.0357, "memory": 0.09158952161669731, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749451289172, "cpu": 0.026400000000000003, "memory": 0.09154193103313446, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749451291172, "cpu": 0.0333, "memory": 0.09321714751422405, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451293173, "cpu": 0.0172, "memory": 0.09200433269143105, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451295174, "cpu": 0.022699999999999998, "memory": 0.09547648951411247, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749451297175, "cpu": 0.0203, "memory": 0.09230202995240688, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749451299176, "cpu": 0.0217, "memory": 0.09524165652692318, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749451301176, "cpu": 0.0231, "memory": 0.09284624829888344, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749451303177, "cpu": 0.030400000000000003, "memory": 0.09595649316906929, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451305177, "cpu": 0.0336, "memory": 0.09433571249246597, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451307178, "cpu": 0.0291, "memory": 0.09710579179227352, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749451309179, "cpu": 0.0364, "memory": 0.09510768577456474, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749451311181, "cpu": 0.07479999999999999, "memory": 0.09812405332922935, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749451313182, "cpu": 0.038900000000000004, "memory": 0.09771264158189297, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451315182, "cpu": 0.0359, "memory": 0.09516910649836063, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749451317182, "cpu": 0.0372, "memory": 0.0977829098701477, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451319184, "cpu": 0.0327, "memory": 0.09778197854757309, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451321185, "cpu": 0.0378, "memory": 0.09470423683524132, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451323184, "cpu": 0.0348, "memory": 0.09718644432723522, "temperature": 37, "efficiency": 65}, {"timestamp": 1749451325185, "cpu": 0.0333, "memory": 0.09512058459222317, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749451327187, "cpu": 0.029599999999999998, "memory": 0.09672800078988075, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749451329188, "cpu": 0.033, "memory": 0.0985301099717617, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451331189, "cpu": 0.0225, "memory": 0.09616543538868427, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451333190, "cpu": 0.0386, "memory": 0.09878580458462238, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451335190, "cpu": 0.038900000000000004, "memory": 0.09728451259434223, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451337191, "cpu": 0.0464, "memory": 0.09976252913475037, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749451339192, "cpu": 0.0297, "memory": 0.09820503182709217, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749451341193, "cpu": 0.0179, "memory": 0.0964196864515543, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749451343195, "cpu": 0.0406, "memory": 0.10074502788484097, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451345195, "cpu": 0.0371, "memory": 0.09864647872745991, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749451347197, "cpu": 0.0397, "memory": 0.10156519711017609, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451349198, "cpu": 0.0441, "memory": 0.10200035758316517, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451351199, "cpu": 0.0436, "memory": 0.0989315565675497, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451353199, "cpu": 0.046, "memory": 0.09822514839470387, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749451355201, "cpu": 0.0398, "memory": 0.10167662985622883, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451357201, "cpu": 0.0339, "memory": 0.09849881753325462, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451359203, "cpu": 0.0468, "memory": 0.10144119150936604, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451361204, "cpu": 0.0398, "memory": 0.0988260842859745, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451363205, "cpu": 0.0547, "memory": 0.10195965878665447, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451365206, "cpu": 0.031100000000000003, "memory": 0.10014805011451244, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451367206, "cpu": 0.026, "memory": 0.10311738587915897, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451369207, "cpu": 0.0189, "memory": 0.101793697103858, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451371208, "cpu": 0.0182, "memory": 0.10053487494587898, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749451373209, "cpu": 0.0184, "memory": 0.10020406916737556, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451375211, "cpu": 0.0223, "memory": 0.10230741463601589, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749451377210, "cpu": 0.0342, "memory": 0.10044863447546959, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749451379211, "cpu": 0.037599999999999995, "memory": 0.10130428709089756, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749451381212, "cpu": 0.0522, "memory": 0.10254504159092903, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749451383213, "cpu": 0.0514, "memory": 0.10291039943695068, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451385214, "cpu": 0.0494, "memory": 0.10131471790373325, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451387215, "cpu": 0.0456, "memory": 0.1028117723762989, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451389216, "cpu": 0.0523, "memory": 0.10483223013579845, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451391217, "cpu": 0.0445, "memory": 0.103389797732234, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451393219, "cpu": 0.0427, "memory": 0.1061983872205019, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749451395221, "cpu": 0.046099999999999995, "memory": 0.10570846498012543, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451397221, "cpu": 0.0524, "memory": 0.10294346138834953, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451399222, "cpu": 0.0222, "memory": 0.10261791758239269, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451401223, "cpu": 0.0354, "memory": 0.10539093054831028, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749451403225, "cpu": 0.0509, "memory": 0.10452833957970142, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451405225, "cpu": 0.0337, "memory": 0.10406612418591976, "temperature": 37, "efficiency": 65}, {"timestamp": 1749451407226, "cpu": 0.0495, "memory": 0.10721753351390362, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451409226, "cpu": 0.0482, "memory": 0.10721264407038689, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451411227, "cpu": 0.0466, "memory": 0.10468768887221813, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451413229, "cpu": 0.0656, "memory": 0.10467995889484882, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749451415230, "cpu": 0.0477, "memory": 0.10837879963219166, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451417231, "cpu": 0.030899999999999997, "memory": 0.1051765400916338, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749451419232, "cpu": 0.049600000000000005, "memory": 0.1076744869351387, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749451421233, "cpu": 0.0286, "memory": 0.10595587082207203, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749451423233, "cpu": 0.0332, "memory": 0.10912162251770496, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451425234, "cpu": 0.034699999999999995, "memory": 0.1081313006579876, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749451427235, "cpu": 0.0357, "memory": 0.1061675138771534, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749451429237, "cpu": 0.0247, "memory": 0.10976465418934822, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749451431237, "cpu": 0.0299, "memory": 0.10742382146418095, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451433239, "cpu": 0.0332, "memory": 0.10693571530282497, "temperature": 37, "efficiency": 65}, {"timestamp": 1749451435239, "cpu": 0.036000000000000004, "memory": 0.10917666368186474, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451437241, "cpu": 0.0334, "memory": 0.10741888545453548, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451439240, "cpu": 0.0286, "memory": 0.10737585835158825, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451441241, "cpu": 0.0283, "memory": 0.10881065391004086, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749451443240, "cpu": 0.0333, "memory": 0.10920306667685509, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451445242, "cpu": 0.033, "memory": 0.1079923938959837, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749451447243, "cpu": 0.0327, "memory": 0.10795663110911846, "temperature": 37, "efficiency": 65.1}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}