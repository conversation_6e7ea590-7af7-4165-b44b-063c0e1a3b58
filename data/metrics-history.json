{"timestamp": 1749470471894, "metrics": {"system": {"cpu": {"usage": 0.0077, "temperature": 37, "cores": 10}, "memory": {"used": 17795960, "total": 17179869184, "efficiency": 25.3997802734375}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749468971932}, "performance": {"uptime": 1498.792, "efficiency": 70.5, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749470272632, "cpu": 0.3007, "memory": 0.12345272116363049, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749470274632, "cpu": 0.0059, "memory": 0.12391936033964157, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749470276632, "cpu": 0.0123, "memory": 0.1250548753887415, "temperature": 37, "efficiency": 65}, {"timestamp": 1749470278633, "cpu": 0.3457, "memory": 0.12687467969954014, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749470280634, "cpu": 0.3236, "memory": 0.12546884827315807, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749470282636, "cpu": 0.0466, "memory": 0.1290352549403906, "temperature": 37, "efficiency": 64}, {"timestamp": 1749470284636, "cpu": 0.218, "memory": 0.1254041213542223, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749470286636, "cpu": 0.0059, "memory": 0.1273367553949356, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749470288637, "cpu": 0.0063, "memory": 0.12577115558087826, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749470290639, "cpu": 0.643, "memory": 0.1271786168217659, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749470292638, "cpu": 0.0053, "memory": 0.12667984701693058, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749470294640, "cpu": 0.0051, "memory": 0.12623160146176815, "temperature": 37, "efficiency": 65}, {"timestamp": 1749470296640, "cpu": 0.21259999999999998, "memory": 0.12581860646605492, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749470298642, "cpu": 1.5635, "memory": 0.13060620985925198, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749470300642, "cpu": 0.19980000000000003, "memory": 0.08717854507267475, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470302644, "cpu": 0.3582, "memory": 0.08667092770338058, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749470304645, "cpu": 0.0051, "memory": 0.08542370051145554, "temperature": 37, "efficiency": 74.8}, {"timestamp": 1749470306647, "cpu": 0.0239, "memory": 0.08852854371070862, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749470308648, "cpu": 0.35760000000000003, "memory": 0.08594933897256851, "temperature": 37, "efficiency": 74.6}, {"timestamp": 1749470310648, "cpu": 0.346, "memory": 0.08543669246137142, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749470312649, "cpu": 0.0075, "memory": 0.08725244551897049, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749470314650, "cpu": 0.1685, "memory": 0.08910275064408779, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749470316650, "cpu": 0.0127, "memory": 0.08611194789409637, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749470318651, "cpu": 0.007600000000000001, "memory": 0.08606156334280968, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749470320651, "cpu": 0.4174, "memory": 0.08698822930455208, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470322654, "cpu": 0.0077, "memory": 0.086994469165802, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749470324654, "cpu": 0.006200000000000001, "memory": 0.08636116981506348, "temperature": 37, "efficiency": 75.5}, {"timestamp": 1749470326656, "cpu": 0.218, "memory": 0.08618193678557873, "temperature": 37, "efficiency": 74.6}, {"timestamp": 1749470328655, "cpu": 0.009899999999999999, "memory": 0.08633527904748917, "temperature": 37, "efficiency": 74.6}, {"timestamp": 1749470330658, "cpu": 0.34390000000000004, "memory": 0.0870170071721077, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470332660, "cpu": 0.2746, "memory": 0.08727293461561203, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470334661, "cpu": 0.01, "memory": 0.08697076700627804, "temperature": 37, "efficiency": 74.5}, {"timestamp": 1749470336661, "cpu": 0.1729, "memory": 0.08854703046381474, "temperature": 37, "efficiency": 74}, {"timestamp": 1749470338665, "cpu": 0.3876, "memory": 0.09038131684064865, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470340665, "cpu": 0.3966, "memory": 0.08903411217033863, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749470342663, "cpu": 0.0085, "memory": 0.09203888475894928, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470344666, "cpu": 0.3183, "memory": 0.08855792693793774, "temperature": 37, "efficiency": 74}, {"timestamp": 1749470346667, "cpu": 0.0126, "memory": 0.09110127575695515, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470348667, "cpu": 0.006200000000000001, "memory": 0.09021349251270294, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470350667, "cpu": 0.515, "memory": 0.09161657653748989, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470352670, "cpu": 0.007600000000000001, "memory": 0.09021400474011898, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470354670, "cpu": 0.0059, "memory": 0.09004585444927216, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470356671, "cpu": 0.1442, "memory": 0.0895956065505743, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749470358673, "cpu": 0.0106, "memory": 0.09027966298162937, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470360673, "cpu": 0.3729, "memory": 0.08952924981713295, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470362674, "cpu": 0.1527, "memory": 0.09003942832350731, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470364674, "cpu": 0.0164, "memory": 0.09422516450285912, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749470366680, "cpu": 0.0134, "memory": 0.09207814000546932, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470368677, "cpu": 0.3365, "memory": 0.09351572953164577, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749470370678, "cpu": 0.3909, "memory": 0.09312373585999012, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749470372678, "cpu": 0.0909, "memory": 0.0954946968704462, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470374679, "cpu": 0.45319999999999994, "memory": 0.09270580485463142, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749470376680, "cpu": 0.0081, "memory": 0.09544650092720985, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470378681, "cpu": 0.011000000000000001, "memory": 0.09544342756271362, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470380682, "cpu": 0.3733, "memory": 0.09170672856271267, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470382684, "cpu": 0.0114, "memory": 0.09566936641931534, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470384684, "cpu": 0.0135, "memory": 0.09397631511092186, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749470386685, "cpu": 0.40159999999999996, "memory": 0.09334376081824303, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470388686, "cpu": 0.016, "memory": 0.09306957945227623, "temperature": 37, "efficiency": 73}, {"timestamp": 1749470390687, "cpu": 0.302, "memory": 0.09708348661661148, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749470392688, "cpu": 0.32780000000000004, "memory": 0.09660981595516205, "temperature": 37, "efficiency": 72}, {"timestamp": 1749470394689, "cpu": 0.0136, "memory": 0.09543010964989662, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470396689, "cpu": 0.0143, "memory": 0.09717466309666634, "temperature": 37, "efficiency": 72}, {"timestamp": 1749470398691, "cpu": 0.31939999999999996, "memory": 0.09447354823350906, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749470400691, "cpu": 0.2986, "memory": 0.09434367530047894, "temperature": 37, "efficiency": 72.6}, {"timestamp": 1749470402693, "cpu": 0.0735, "memory": 0.0971830915659666, "temperature": 37, "efficiency": 72}, {"timestamp": 1749470404692, "cpu": 0.40169999999999995, "memory": 0.09854431264102459, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470406695, "cpu": 0.0089, "memory": 0.09584799408912659, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749470408694, "cpu": 0.010799999999999999, "memory": 0.09554657153785229, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749470410696, "cpu": 0.6518999999999999, "memory": 0.09690430015325546, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749470412697, "cpu": 0.013999999999999999, "memory": 0.09695487096905708, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749470414698, "cpu": 0.0173, "memory": 0.09644892998039722, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749470416699, "cpu": 0.5037, "memory": 0.0957112293690443, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470418701, "cpu": 0.053, "memory": 0.10050344280898571, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749470420701, "cpu": 0.3786, "memory": 0.09963363409042358, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749470422703, "cpu": 0.5197, "memory": 0.09941663593053818, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749470424704, "cpu": 0.0177, "memory": 0.0985938124358654, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749470426704, "cpu": 0.0091, "memory": 0.09711901657283306, "temperature": 37, "efficiency": 72}, {"timestamp": 1749470428704, "cpu": 0.4766, "memory": 0.09875153191387653, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470430706, "cpu": 0.42040000000000005, "memory": 0.09868987835943699, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470432706, "cpu": 0.0078, "memory": 0.10108370333909988, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749470434707, "cpu": 0.3595, "memory": 0.10260897688567638, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749470436708, "cpu": 0.015, "memory": 0.09944392368197441, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470438710, "cpu": 0.012400000000000001, "memory": 0.09924457408487797, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470440711, "cpu": 0.40549999999999997, "memory": 0.09849751368165016, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749470442712, "cpu": 0.0089, "memory": 0.10066903196275234, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749470444713, "cpu": 0.0057, "memory": 0.09933914989233017, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749470446713, "cpu": 0.005, "memory": 0.10267780162394047, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749470448714, "cpu": 0.0049, "memory": 0.09961524046957493, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749470450714, "cpu": 0.0065, "memory": 0.10190056636929512, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749470452715, "cpu": 0.007899999999999999, "memory": 0.10131746530532837, "temperature": 37, "efficiency": 71}, {"timestamp": 1749470454716, "cpu": 0.0060999999999999995, "memory": 0.10074344463646412, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749470456716, "cpu": 0.0057, "memory": 0.10219532996416092, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749470458718, "cpu": 0.0089, "memory": 0.10181381367146969, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749470460720, "cpu": 0.0052, "memory": 0.10063783265650272, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749470462721, "cpu": 0.0081, "memory": 0.10139523074030876, "temperature": 37, "efficiency": 71}, {"timestamp": 1749470464720, "cpu": 0.0063999999999999994, "memory": 0.10110894218087196, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749470466722, "cpu": 0.0315, "memory": 0.10585836134850979, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749470468722, "cpu": 0.005399999999999999, "memory": 0.10218913666903973, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749470470724, "cpu": 0.0077, "memory": 0.10358612053096294, "temperature": 37, "efficiency": 70.5}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}