{"timestamp": 1749469571903, "metrics": {"system": {"cpu": {"usage": 0.005, "temperature": 37, "cores": 10}, "memory": {"used": 20680400, "total": 17179869184, "efficiency": 4.952451407191262}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749468971932}, "performance": {"uptime": 598.353, "efficiency": 63.6, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749469372177, "cpu": 0.017, "memory": 0.08141519501805305, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749469374180, "cpu": 0.0082, "memory": 0.08259527385234833, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749469376181, "cpu": 0.018799999999999997, "memory": 0.08164132013916969, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749469378182, "cpu": 0.009000000000000001, "memory": 0.08485745638608932, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749469380182, "cpu": 0.0291, "memory": 0.08588633500039577, "temperature": 37, "efficiency": 73}, {"timestamp": 1749469382183, "cpu": 0.0312, "memory": 0.08637658320367336, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749469384184, "cpu": 0.0114, "memory": 0.08479622192680836, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749469386186, "cpu": 0.0087, "memory": 0.08300519548356533, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749469388187, "cpu": 0.0053, "memory": 0.08561392314732075, "temperature": 37, "efficiency": 72}, {"timestamp": 1749469390187, "cpu": 0.0146, "memory": 0.0870188232511282, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749469392189, "cpu": 0.011000000000000001, "memory": 0.08732411079108715, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749469394190, "cpu": 0.007899999999999999, "memory": 0.08650962263345718, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749469396190, "cpu": 0.0056, "memory": 0.08666538633406162, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749469398191, "cpu": 0.0053, "memory": 0.08543496951460838, "temperature": 37, "efficiency": 72}, {"timestamp": 1749469400192, "cpu": 0.005, "memory": 0.0857875682413578, "temperature": 37, "efficiency": 73}, {"timestamp": 1749469402194, "cpu": 0.0053, "memory": 0.08853347972035408, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749469404194, "cpu": 0.008, "memory": 0.08907048031687737, "temperature": 37, "efficiency": 71}, {"timestamp": 1749469406195, "cpu": 0.0077, "memory": 0.09057270362973213, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749469408195, "cpu": 0.0093, "memory": 0.08940021507441998, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749469410197, "cpu": 0.0114, "memory": 0.09173122234642506, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749469412197, "cpu": 0.0101, "memory": 0.09281369857490063, "temperature": 37, "efficiency": 70}, {"timestamp": 1749469414198, "cpu": 0.0204, "memory": 0.09168130345642567, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749469416199, "cpu": 0.009899999999999999, "memory": 0.09364751167595387, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749469418201, "cpu": 0.3378, "memory": 0.09466931223869324, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749469420201, "cpu": 0.0239, "memory": 0.0929337926208973, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749469422202, "cpu": 0.0138, "memory": 0.09731804020702839, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749469424203, "cpu": 0.0102, "memory": 0.09729480370879173, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749469426204, "cpu": 0.005, "memory": 0.09544338099658489, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749469428205, "cpu": 0.0092, "memory": 0.09699543006718159, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749469430208, "cpu": 0.010799999999999999, "memory": 0.09701494127511978, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749469432208, "cpu": 0.0069, "memory": 0.09827865287661552, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749469434208, "cpu": 0.0092, "memory": 0.10055163875222206, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749469436222, "cpu": 0.009399999999999999, "memory": 0.09986660443246365, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749469438222, "cpu": 0.0060999999999999995, "memory": 0.09873243980109692, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749469440223, "cpu": 0.0073999999999999995, "memory": 0.10108938440680504, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749469442224, "cpu": 0.0121, "memory": 0.09960304014384747, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749469444226, "cpu": 0.0156, "memory": 0.10369685478508472, "temperature": 37, "efficiency": 67}, {"timestamp": 1749469446227, "cpu": 0.006600000000000001, "memory": 0.10110568255186081, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749469448228, "cpu": 0.0073, "memory": 0.10086558759212494, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749469450229, "cpu": 0.0156, "memory": 0.10330909863114357, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749469452231, "cpu": 0.0093, "memory": 0.10164491832256317, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749469454231, "cpu": 0.0087, "memory": 0.10435241274535656, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749469456233, "cpu": 0.0257, "memory": 0.10224566794931889, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749469458235, "cpu": 0.007600000000000001, "memory": 0.1045821700245142, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749469460235, "cpu": 0.0072, "memory": 0.105279590934515, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749469462235, "cpu": 0.0655, "memory": 0.10678726248443127, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749469464236, "cpu": 0.0072, "memory": 0.10542566888034344, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749469466237, "cpu": 0.0067, "memory": 0.10451488196849823, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749469468238, "cpu": 0.0127, "memory": 0.10524429380893707, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749469470239, "cpu": 0.0084, "memory": 0.10826354846358299, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749469472240, "cpu": 0.0057, "memory": 0.10596513748168945, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749469474241, "cpu": 0.0085, "memory": 0.10861479677259922, "temperature": 37, "efficiency": 66}, {"timestamp": 1749469476242, "cpu": 0.0109, "memory": 0.10613924823701382, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749469478243, "cpu": 0.0117, "memory": 0.10908646509051323, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469480245, "cpu": 0.0111, "memory": 0.10719643905758858, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749469482245, "cpu": 0.010799999999999999, "memory": 0.10856525041162968, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749469484246, "cpu": 0.0107, "memory": 0.10957969352602959, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749469486247, "cpu": 0.0063999999999999994, "memory": 0.10774200782179832, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749469488247, "cpu": 0.0177, "memory": 0.10774745605885983, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749469490247, "cpu": 0.0146, "memory": 0.1086648553609848, "temperature": 37, "efficiency": 66}, {"timestamp": 1749469492249, "cpu": 0.0161, "memory": 0.10838634334504604, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749469494250, "cpu": 0.0142, "memory": 0.11114724911749363, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749469496251, "cpu": 0.015899999999999997, "memory": 0.11188117787241936, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749469498252, "cpu": 0.0215, "memory": 0.11152229271829128, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749469500253, "cpu": 0.0185, "memory": 0.1090826466679573, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469502254, "cpu": 0.0135, "memory": 0.11202646419405937, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749469504255, "cpu": 0.0184, "memory": 0.11010989546775818, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469506256, "cpu": 0.0161, "memory": 0.11291536502540112, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749469508256, "cpu": 0.0127, "memory": 0.11050417087972164, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749469510258, "cpu": 0.009600000000000001, "memory": 0.11337432079017162, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469512259, "cpu": 0.0114, "memory": 0.11393665336072445, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469514260, "cpu": 0.023, "memory": 0.11088927276432514, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749469516261, "cpu": 0.0118, "memory": 0.11342535726726055, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469518261, "cpu": 0.0067, "memory": 0.1131993718445301, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469520262, "cpu": 0.0138, "memory": 0.11395933106541634, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469522263, "cpu": 0.012799999999999999, "memory": 0.11499659158289433, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749469524264, "cpu": 0.0147, "memory": 0.11350535787642002, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469526265, "cpu": 0.0201, "memory": 0.11520427651703358, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469528267, "cpu": 0.0185, "memory": 0.11530248448252678, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749469530268, "cpu": 0.018699999999999998, "memory": 0.11370005086064339, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469532269, "cpu": 0.0167, "memory": 0.11646836064755917, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749469534269, "cpu": 0.0123, "memory": 0.11432827450335026, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469536271, "cpu": 0.0060999999999999995, "memory": 0.11695325374603271, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749469538271, "cpu": 0.0060999999999999995, "memory": 0.11477828957140446, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469540273, "cpu": 0.0055000000000000005, "memory": 0.11730301193892956, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749469542273, "cpu": 0.0057, "memory": 0.11815582402050495, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749469544274, "cpu": 0.0063, "memory": 0.11433297768235207, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749469546275, "cpu": 0.0103, "memory": 0.11711339466273785, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469548276, "cpu": 0.0111, "memory": 0.11736410669982433, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749469550277, "cpu": 0.0075, "memory": 0.11822143569588661, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749469552278, "cpu": 0.009399999999999999, "memory": 0.1179208979010582, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749469554279, "cpu": 0.006999999999999999, "memory": 0.11611930094659328, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469556280, "cpu": 0.0107, "memory": 0.11678282171487808, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749469558280, "cpu": 0.0068, "memory": 0.11822939850389957, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749469560281, "cpu": 0.0053, "memory": 0.11661122553050518, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469562282, "cpu": 0.0189, "memory": 0.11962284334003925, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749469564283, "cpu": 0.0243, "memory": 0.1172682736068964, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469566284, "cpu": 0.006200000000000001, "memory": 0.12006876058876514, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749469568285, "cpu": 0.0104, "memory": 0.11747409589588642, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469570285, "cpu": 0.005, "memory": 0.12037577107548714, "temperature": 37, "efficiency": 63.6}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}