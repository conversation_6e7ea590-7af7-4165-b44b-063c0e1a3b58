{"timestamp": 1749469871901, "metrics": {"system": {"cpu": {"usage": 0.5006, "temperature": 37, "cores": 10}, "memory": {"used": 18739024, "total": 17179869184, "efficiency": 7.164021281452932}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749468971932}, "performance": {"uptime": 898.48, "efficiency": 64.2, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749469672333, "cpu": 0.0043, "memory": 0.08125556632876396, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749469674335, "cpu": 0.0081, "memory": 0.08399011567234993, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749469676336, "cpu": 0.0052, "memory": 0.08466886356472969, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749469678336, "cpu": 0.0053, "memory": 0.0815605279058218, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749469680337, "cpu": 0.0072, "memory": 0.08244984783232212, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749469682338, "cpu": 0.0059, "memory": 0.08380771614611149, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749469684339, "cpu": 0.0060999999999999995, "memory": 0.08168318308889866, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749469686341, "cpu": 0.0059, "memory": 0.08305232040584087, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749469688341, "cpu": 0.0092, "memory": 0.0842757523059845, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469690341, "cpu": 0.46449999999999997, "memory": 0.08544661104679108, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749469692343, "cpu": 0.022000000000000002, "memory": 0.08450658060610294, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469694342, "cpu": 0.0104, "memory": 0.08491114713251591, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749469696344, "cpu": 0.010799999999999999, "memory": 0.08596451953053474, "temperature": 37, "efficiency": 65}, {"timestamp": 1749469698344, "cpu": 0.0119, "memory": 0.08440092206001282, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749469700343, "cpu": 0.0087, "memory": 0.0839852262288332, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749469702344, "cpu": 0.0189, "memory": 0.08581597357988358, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469704346, "cpu": 0.0136, "memory": 0.08628037758171558, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749469706347, "cpu": 0.0106, "memory": 0.0893463846296072, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469708347, "cpu": 0.0052, "memory": 0.08619166910648346, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469710347, "cpu": 0.0045000000000000005, "memory": 0.08676867000758648, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749469712347, "cpu": 0.0817, "memory": 0.08608163334429264, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469714348, "cpu": 0.0052, "memory": 0.09037517011165619, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469716348, "cpu": 0.0114, "memory": 0.08810735307633877, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749469718348, "cpu": 0.0083, "memory": 0.08724010549485683, "temperature": 37, "efficiency": 66}, {"timestamp": 1749469720349, "cpu": 0.0052, "memory": 0.09027994237840176, "temperature": 37, "efficiency": 65}, {"timestamp": 1749469722350, "cpu": 0.007600000000000001, "memory": 0.09069475345313549, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469724350, "cpu": 0.0069, "memory": 0.09017852135002613, "temperature": 37, "efficiency": 65}, {"timestamp": 1749469726351, "cpu": 0.0063, "memory": 0.09128176607191563, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469728351, "cpu": 0.0086, "memory": 0.08943304419517517, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749469730353, "cpu": 0.0063, "memory": 0.08905986323952675, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469732353, "cpu": 0.007600000000000001, "memory": 0.09073973633348942, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749469734355, "cpu": 0.0087, "memory": 0.09143734350800514, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749469736355, "cpu": 0.009000000000000001, "memory": 0.08921404369175434, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749469738356, "cpu": 0.0106, "memory": 0.09073386900126934, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749469740356, "cpu": 0.0073, "memory": 0.09127692319452763, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469742358, "cpu": 0.0087, "memory": 0.09043412283062935, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749469744359, "cpu": 0.0058, "memory": 0.09340462274849415, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749469746359, "cpu": 0.0092, "memory": 0.09124376811087132, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469748360, "cpu": 0.0117, "memory": 0.08981078863143921, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749469750360, "cpu": 0.0056, "memory": 0.09328043088316917, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749469752361, "cpu": 0.006200000000000001, "memory": 0.09362907148897648, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469754361, "cpu": 0.0093, "memory": 0.09335251525044441, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749469756363, "cpu": 0.012199999999999999, "memory": 0.09444211609661579, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469758366, "cpu": 0.0177, "memory": 0.09267195127904415, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749469760367, "cpu": 0.015899999999999997, "memory": 0.09254603646695614, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749469762367, "cpu": 0.0109, "memory": 0.09417254477739334, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469764368, "cpu": 0.0157, "memory": 0.09497669525444508, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469766367, "cpu": 0.0089, "memory": 0.09737606160342693, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749469768367, "cpu": 0.0117, "memory": 0.09422274306416512, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749469770369, "cpu": 0.0114, "memory": 0.09477841667830944, "temperature": 37, "efficiency": 67}, {"timestamp": 1749469772371, "cpu": 0.0088, "memory": 0.09819441474974155, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469774370, "cpu": 0.0065, "memory": 0.09708204306662083, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469776372, "cpu": 0.0084, "memory": 0.09534168057143688, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749469778374, "cpu": 0.012199999999999999, "memory": 0.09880373254418373, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469780374, "cpu": 0.009399999999999999, "memory": 0.0977210234850645, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469782376, "cpu": 0.0105, "memory": 0.09878650307655334, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469784376, "cpu": 0.0086, "memory": 0.09852200746536255, "temperature": 37, "efficiency": 65}, {"timestamp": 1749469786376, "cpu": 0.0055000000000000005, "memory": 0.09963694028556347, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469788376, "cpu": 0.0060999999999999995, "memory": 0.09800968691706657, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749469790378, "cpu": 0.0068, "memory": 0.09884657338261604, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749469792377, "cpu": 0.0302, "memory": 0.1002684235572815, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749469794379, "cpu": 0.0073999999999999995, "memory": 0.09698686189949512, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749469796378, "cpu": 0.0073, "memory": 0.09986911900341511, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469798380, "cpu": 0.0048000000000000004, "memory": 0.09710490703582764, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749469800381, "cpu": 0.0089, "memory": 0.09771394543349743, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749469802382, "cpu": 0.039, "memory": 0.10140431113541126, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469804383, "cpu": 0.0097, "memory": 0.10025906376540661, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749469806384, "cpu": 0.0145, "memory": 0.09865909814834595, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749469808384, "cpu": 0.0287, "memory": 0.10195253416895866, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469810386, "cpu": 0.006200000000000001, "memory": 0.10090633295476437, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469812386, "cpu": 0.006600000000000001, "memory": 0.10112333111464977, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469814387, "cpu": 0.0086, "memory": 0.1010566484183073, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469816388, "cpu": 0.0056, "memory": 0.1020338386297226, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469818389, "cpu": 0.0088, "memory": 0.10071718133985996, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469820391, "cpu": 0.0087, "memory": 0.10153991170227528, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749469822391, "cpu": 0.046099999999999995, "memory": 0.10320995934307575, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469824392, "cpu": 0.008, "memory": 0.1034165732562542, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749469826393, "cpu": 0.0095, "memory": 0.10178904049098492, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749469828393, "cpu": 0.1879, "memory": 0.10305354371666908, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469830395, "cpu": 0.012, "memory": 0.10318895801901817, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749469832395, "cpu": 0.012799999999999999, "memory": 0.10241572745144367, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749469834397, "cpu": 0.48269999999999996, "memory": 0.10592765174806118, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749469836398, "cpu": 0.0162, "memory": 0.1049630343914032, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749469838399, "cpu": 0.4246, "memory": 0.10306793265044689, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749469840399, "cpu": 0.395, "memory": 0.10625850409269333, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469842401, "cpu": 0.0169, "memory": 0.10675913654267788, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749469844401, "cpu": 0.0162, "memory": 0.10645613074302673, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749469846401, "cpu": 0.1682, "memory": 0.10766061022877693, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749469848401, "cpu": 0.1735, "memory": 0.1066028606146574, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749469850404, "cpu": 0.014799999999999999, "memory": 0.10660099796950817, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469852404, "cpu": 0.3094, "memory": 0.10377173312008381, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749469854406, "cpu": 0.012899999999999998, "memory": 0.10487535037100315, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749469856405, "cpu": 0.015899999999999997, "memory": 0.10778270661830902, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749469858408, "cpu": 0.8215, "memory": 0.10495427995920181, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749469860408, "cpu": 0.0151, "memory": 0.10557081550359726, "temperature": 37, "efficiency": 65}, {"timestamp": 1749469862409, "cpu": 0.01, "memory": 0.10484145022928715, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749469864409, "cpu": 0.4057, "memory": 0.10807542130351067, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749469866409, "cpu": 0.013, "memory": 0.10639568790793419, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749469868411, "cpu": 0.3922, "memory": 0.10545128025114536, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749469870412, "cpu": 0.5006, "memory": 0.10907547548413277, "temperature": 37, "efficiency": 64.2}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}