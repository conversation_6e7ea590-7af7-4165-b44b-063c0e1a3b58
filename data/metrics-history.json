{"timestamp": 1749450547956, "metrics": {"system": {"cpu": {"usage": 0.0157, "temperature": 37, "cores": 10}, "memory": {"used": 19167256, "total": 17179869184, "efficiency": 13.91637491951802}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749449348022}, "performance": {"uptime": 1198.751, "efficiency": 66.6, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749450348595, "cpu": 0.022000000000000002, "memory": 0.09735939092934132, "temperature": 37, "efficiency": 68}, {"timestamp": 1749450350596, "cpu": 0.0139, "memory": 0.10008742101490498, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749450352597, "cpu": 0.0173, "memory": 0.10073771700263023, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749450354600, "cpu": 0.0194, "memory": 0.09941626340150833, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749450356601, "cpu": 0.0225, "memory": 0.10126563720405102, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749450358602, "cpu": 0.0179, "memory": 0.09835977107286453, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749450360602, "cpu": 0.02, "memory": 0.10085632093250751, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749450362605, "cpu": 0.0207, "memory": 0.09865029715001583, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749450364608, "cpu": 0.0183, "memory": 0.10036812163889408, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749450366610, "cpu": 0.0162, "memory": 0.10244646109640598, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450368611, "cpu": 0.0172, "memory": 0.10225302539765835, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749450370613, "cpu": 0.1296, "memory": 0.09945034980773926, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749450372612, "cpu": 0.021900000000000003, "memory": 0.10134642943739891, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749450374615, "cpu": 0.018799999999999997, "memory": 0.09993202984333038, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749450376616, "cpu": 0.017499999999999998, "memory": 0.10062903165817261, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749450378618, "cpu": 0.0169, "memory": 0.10341731831431389, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450380619, "cpu": 0.0214, "memory": 0.10104505345225334, "temperature": 37, "efficiency": 67}, {"timestamp": 1749450382623, "cpu": 0.11399999999999999, "memory": 0.10206233710050583, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749450384624, "cpu": 0.0208, "memory": 0.10003438219428062, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749450386624, "cpu": 0.019799999999999998, "memory": 0.10382691398262978, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450388625, "cpu": 0.0179, "memory": 0.10079601779580116, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749450390628, "cpu": 0.0183, "memory": 0.10366463102400303, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450392629, "cpu": 0.019, "memory": 0.10094363242387772, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749450394632, "cpu": 0.11670000000000001, "memory": 0.1031417865306139, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749450396633, "cpu": 0.0174, "memory": 0.10077394545078278, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749450398636, "cpu": 0.017499999999999998, "memory": 0.10440628975629807, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749450400637, "cpu": 0.0186, "memory": 0.10114964097738266, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749450402639, "cpu": 0.0169, "memory": 0.10323752649128437, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749450404639, "cpu": 0.0127, "memory": 0.1017233356833458, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749450406641, "cpu": 0.018699999999999998, "memory": 0.10266639292240143, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749450408642, "cpu": 0.0178, "memory": 0.10504615493118763, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450410645, "cpu": 0.08499999999999999, "memory": 0.1034329179674387, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749450412646, "cpu": 0.0714, "memory": 0.1042831689119339, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749450414649, "cpu": 0.0182, "memory": 0.10296381078660488, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749450416651, "cpu": 0.0155, "memory": 0.10499861091375351, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450418651, "cpu": 0.0172, "memory": 0.10190149769186974, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749450420654, "cpu": 0.0164, "memory": 0.10458175092935562, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749450422656, "cpu": 0.0177, "memory": 0.1024298369884491, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749450424659, "cpu": 0.0145, "memory": 0.10435031726956367, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749450426659, "cpu": 0.0197, "memory": 0.1065659336745739, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450428660, "cpu": 0.0142, "memory": 0.1051721628755331, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749450430662, "cpu": 0.0196, "memory": 0.10680640116333961, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749450432665, "cpu": 0.0161, "memory": 0.10423371568322182, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749450434667, "cpu": 0.0142, "memory": 0.10738135315477848, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450436667, "cpu": 0.019, "memory": 0.10360684245824814, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749450438667, "cpu": 0.016300000000000002, "memory": 0.10639340616762638, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450440672, "cpu": 0.0136, "memory": 0.10472205467522144, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749450442675, "cpu": 0.015200000000000002, "memory": 0.10563945397734642, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749450444677, "cpu": 0.0136, "memory": 0.10419022291898727, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749450446679, "cpu": 0.014100000000000001, "memory": 0.10815081186592579, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749450448681, "cpu": 0.0085, "memory": 0.10503334924578667, "temperature": 37, "efficiency": 67}, {"timestamp": 1749450450681, "cpu": 0.013300000000000001, "memory": 0.10794405825436115, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749450452684, "cpu": 0.0143, "memory": 0.10550236329436302, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749450454685, "cpu": 0.0097, "memory": 0.10748868808150291, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450456687, "cpu": 0.0112, "memory": 0.10516056790947914, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749450458688, "cpu": 0.3144, "memory": 0.10901023633778095, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749450460692, "cpu": 0.0115, "memory": 0.10656779631972313, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749450462693, "cpu": 0.0117, "memory": 0.10872487910091877, "temperature": 37, "efficiency": 66}, {"timestamp": 1749450464694, "cpu": 0.0127, "memory": 0.10675135999917984, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749450466697, "cpu": 0.014100000000000001, "memory": 0.10772380046546459, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450468698, "cpu": 0.0142, "memory": 0.10586800053715706, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749450470702, "cpu": 0.0157, "memory": 0.10866778902709484, "temperature": 37, "efficiency": 66}, {"timestamp": 1749450472703, "cpu": 0.0145, "memory": 0.1093866303563118, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749450474704, "cpu": 0.0107, "memory": 0.10820385068655014, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749450476707, "cpu": 0.0138, "memory": 0.11009187437593937, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749450478710, "cpu": 0.015899999999999997, "memory": 0.10691438801586628, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749450480712, "cpu": 0.014899999999999998, "memory": 0.10953648015856743, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749450482712, "cpu": 0.013300000000000001, "memory": 0.10727131739258766, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749450484713, "cpu": 0.0162, "memory": 0.10905838571488857, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749450486715, "cpu": 0.013200000000000002, "memory": 0.10690051130950451, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749450488717, "cpu": 0.014100000000000001, "memory": 0.11045439168810844, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749450490719, "cpu": 0.009899999999999999, "memory": 0.10755183175206184, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450492721, "cpu": 0.0107, "memory": 0.10965880937874317, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749450494723, "cpu": 0.0116, "memory": 0.10851710103452206, "temperature": 37, "efficiency": 66}, {"timestamp": 1749450496725, "cpu": 0.009600000000000001, "memory": 0.10919328778982162, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749450498727, "cpu": 0.0123, "memory": 0.10743588209152222, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749450500729, "cpu": 0.0139, "memory": 0.11016847565770149, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749450502730, "cpu": 0.013, "memory": 0.1111084595322609, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749450504731, "cpu": 0.012799999999999999, "memory": 0.1098821870982647, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749450506734, "cpu": 0.0139, "memory": 0.10907393880188465, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749450508736, "cpu": 0.0135, "memory": 0.11039720848202705, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749450510739, "cpu": 0.016, "memory": 0.10903095826506615, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749450512742, "cpu": 0.013999999999999999, "memory": 0.1110919751226902, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749450514743, "cpu": 0.012799999999999999, "memory": 0.10868706740438938, "temperature": 37, "efficiency": 66}, {"timestamp": 1749450516746, "cpu": 0.0112, "memory": 0.11075981892645359, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749450518748, "cpu": 0.015, "memory": 0.11009550653398037, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749450520750, "cpu": 0.0182, "memory": 0.11142403818666935, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749450522751, "cpu": 0.0118, "memory": 0.10899361222982407, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749450524754, "cpu": 0.0156, "memory": 0.11220877058804035, "temperature": 37, "efficiency": 65}, {"timestamp": 1749450526756, "cpu": 0.015799999999999998, "memory": 0.11320561170578003, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749450528757, "cpu": 0.013300000000000001, "memory": 0.11075199581682682, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749450530760, "cpu": 0.0145, "memory": 0.1137870829552412, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749450532760, "cpu": 0.0144, "memory": 0.1100453082472086, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749450534762, "cpu": 0.0155, "memory": 0.11337832547724247, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749450536764, "cpu": 0.0171, "memory": 0.1105818897485733, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749450538766, "cpu": 0.015899999999999997, "memory": 0.1121198758482933, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749450540767, "cpu": 0.012400000000000001, "memory": 0.11047627776861191, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749450542768, "cpu": 0.012899999999999998, "memory": 0.11267829686403275, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749450544772, "cpu": 0.012899999999999998, "memory": 0.11441120877861977, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749450546773, "cpu": 0.0157, "memory": 0.11156811378896236, "temperature": 37, "efficiency": 66.6}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}