{"timestamp": 1749448929670, "metrics": {"system": {"cpu": {"usage": 0.039599999999999996, "temperature": 37, "cores": 10}, "memory": {"used": 18058688, "total": 17179869184, "efficiency": 8.072156484570485}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749448629667}, "performance": {"uptime": 298.26, "efficiency": 64.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749448729805, "cpu": 0.05, "memory": 0.08176001720130444, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749448731806, "cpu": 0.054900000000000004, "memory": 0.0849385280162096, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448733807, "cpu": 0.08009999999999999, "memory": 0.08462732657790184, "temperature": 37, "efficiency": 65}, {"timestamp": 1749448735808, "cpu": 0.0388, "memory": 0.0830579549074173, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749448737809, "cpu": 0.0489, "memory": 0.08634594269096851, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749448739810, "cpu": 0.0448, "memory": 0.08559380657970905, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749448741811, "cpu": 0.0556, "memory": 0.08532772772014141, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749448743810, "cpu": 0.0434, "memory": 0.08337022736668587, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749448745811, "cpu": 0.0661, "memory": 0.0871285330504179, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749448747813, "cpu": 0.0969, "memory": 0.08730422705411911, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749448749814, "cpu": 0.051500000000000004, "memory": 0.08710287511348724, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749448751815, "cpu": 0.047, "memory": 0.08665136992931366, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749448753814, "cpu": 0.0536, "memory": 0.08678529411554337, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749448755815, "cpu": 0.0949, "memory": 0.0849379226565361, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749448757817, "cpu": 0.0998, "memory": 0.08921688422560692, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749448759818, "cpu": 0.0375, "memory": 0.08899178355932236, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448761818, "cpu": 0.0343, "memory": 0.08757137693464756, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749448763818, "cpu": 0.0324, "memory": 0.08576181717216969, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749448765818, "cpu": 0.0419, "memory": 0.08625555783510208, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749448767818, "cpu": 0.0665, "memory": 0.08952566422522068, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749448769820, "cpu": 0.0513, "memory": 0.08916081860661507, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749448771821, "cpu": 0.1784, "memory": 0.08923686109483242, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448773821, "cpu": 0.0941, "memory": 0.0867730937898159, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749448775822, "cpu": 0.053899999999999997, "memory": 0.0909644179046154, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749448777824, "cpu": 0.0846, "memory": 0.09069675579667091, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448779824, "cpu": 0.3126, "memory": 0.09050117805600166, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448781826, "cpu": 0.1, "memory": 0.09052306413650513, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448783826, "cpu": 0.0398, "memory": 0.09033484384417534, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749448785827, "cpu": 0.0701, "memory": 0.08832737803459167, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749448787827, "cpu": 0.052700000000000004, "memory": 0.08831741288304329, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749448789829, "cpu": 0.046700000000000005, "memory": 0.08858875371515751, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749448791830, "cpu": 0.0675, "memory": 0.09245108813047409, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749448793831, "cpu": 0.049, "memory": 0.09132563136518002, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749448795837, "cpu": 0.1849, "memory": 0.09128921665251255, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749448797835, "cpu": 0.0444, "memory": 0.08965493179857731, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749448799835, "cpu": 0.0824, "memory": 0.08937153033912182, "temperature": 37, "efficiency": 67}, {"timestamp": 1749448801836, "cpu": 0.0446, "memory": 0.08983477018773556, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749448803836, "cpu": 0.047, "memory": 0.09304536506533623, "temperature": 37, "efficiency": 64}, {"timestamp": 1749448805838, "cpu": 0.0777, "memory": 0.09277286008000374, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749448807839, "cpu": 0.0452, "memory": 0.092674745246768, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749448809840, "cpu": 0.1015, "memory": 0.0935673713684082, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448811840, "cpu": 0.10300000000000001, "memory": 0.09364606812596321, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448813849, "cpu": 0.0785, "memory": 0.09318836964666843, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448815841, "cpu": 0.0635, "memory": 0.09160968475043774, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749448817865, "cpu": 0.0713, "memory": 0.09140395559370518, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749448819859, "cpu": 0.0701, "memory": 0.09162281639873981, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749448821860, "cpu": 0.0935, "memory": 0.09561479091644287, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749448823861, "cpu": 0.055999999999999994, "memory": 0.09489939548075199, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448825862, "cpu": 0.09380000000000001, "memory": 0.09253132157027721, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749448827863, "cpu": 0.05, "memory": 0.09665833786129951, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749448829880, "cpu": 0.6392, "memory": 0.09638159535825253, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749448831880, "cpu": 0.07339999999999999, "memory": 0.09659486822783947, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749448833881, "cpu": 0.07780000000000001, "memory": 0.09452216327190399, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749448835882, "cpu": 0.0423, "memory": 0.09439745917916298, "temperature": 37, "efficiency": 65}, {"timestamp": 1749448837883, "cpu": 0.0406, "memory": 0.0945044681429863, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749448839884, "cpu": 0.0456, "memory": 0.09564338251948357, "temperature": 37, "efficiency": 65}, {"timestamp": 1749448841885, "cpu": 0.0919, "memory": 0.09540878236293793, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749448843886, "cpu": 0.041100000000000005, "memory": 0.09516142308712006, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749448845887, "cpu": 0.0776, "memory": 0.09908578358590603, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448847888, "cpu": 0.05159999999999999, "memory": 0.09910441003739834, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448849889, "cpu": 0.0672, "memory": 0.09905649349093437, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448851889, "cpu": 0.1846, "memory": 0.09867604821920395, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448853891, "cpu": 0.1215, "memory": 0.09863958694040775, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749448855892, "cpu": 0.2069, "memory": 0.09894431568682194, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749448857892, "cpu": 0.2296, "memory": 0.0977509655058384, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749448859894, "cpu": 0.0601, "memory": 0.09754560887813568, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749448861896, "cpu": 0.0813, "memory": 0.09778439998626709, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749448863897, "cpu": 0.1523, "memory": 0.10145301930606365, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749448865898, "cpu": 0.1804, "memory": 0.0972997397184372, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749448867898, "cpu": 0.08109999999999999, "memory": 0.09761573746800423, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749448869899, "cpu": 0.25170000000000003, "memory": 0.10046777315437794, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448871901, "cpu": 0.0414, "memory": 0.10184580460190773, "temperature": 37, "efficiency": 64}, {"timestamp": 1749448873902, "cpu": 0.0709, "memory": 0.1012725755572319, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749448875902, "cpu": 0.4371, "memory": 0.09976346045732498, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749448877904, "cpu": 0.10490000000000001, "memory": 0.10277535766363144, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749448879906, "cpu": 0.06530000000000001, "memory": 0.0989813357591629, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749448881907, "cpu": 0.2996, "memory": 0.10361373424530029, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749448883908, "cpu": 0.0939, "memory": 0.0993743073195219, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749448885908, "cpu": 0.16119999999999998, "memory": 0.10277461260557175, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749448887910, "cpu": 0.2222, "memory": 0.10259845294058323, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749448889910, "cpu": 0.092, "memory": 0.10372973047196865, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749448891913, "cpu": 0.0638, "memory": 0.10440796613693237, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749448893912, "cpu": 0.07339999999999999, "memory": 0.10359752923250198, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749448895913, "cpu": 0.3607, "memory": 0.1043680589646101, "temperature": 37, "efficiency": 64}, {"timestamp": 1749448897912, "cpu": 0.0635, "memory": 0.1011858694255352, "temperature": 37, "efficiency": 65}, {"timestamp": 1749448899913, "cpu": 0.3357, "memory": 0.10456237941980362, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749448901916, "cpu": 0.0782, "memory": 0.10576457716524601, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749448903916, "cpu": 0.0853, "memory": 0.10182489641010761, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749448905916, "cpu": 0.2503, "memory": 0.10612960904836655, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749448907918, "cpu": 0.11889999999999999, "memory": 0.1064058393239975, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749448909919, "cpu": 0.0624, "memory": 0.10256282985210419, "temperature": 37, "efficiency": 65}, {"timestamp": 1749448911920, "cpu": 0.1919, "memory": 0.10262667201459408, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749448913921, "cpu": 0.050699999999999995, "memory": 0.10281768627464771, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749448915923, "cpu": 0.23670000000000002, "memory": 0.10324004106223583, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749448917923, "cpu": 0.1741, "memory": 0.1031187828630209, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749448919923, "cpu": 0.025500000000000002, "memory": 0.10358584113419056, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749448921924, "cpu": 0.0205, "memory": 0.1044602133333683, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749448923925, "cpu": 0.11080000000000001, "memory": 0.10375385172665119, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749448925926, "cpu": 0.0958, "memory": 0.10415795259177685, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749448927927, "cpu": 0.039599999999999996, "memory": 0.10511539876461029, "temperature": 37, "efficiency": 64.7}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}