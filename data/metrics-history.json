{"timestamp": 1749451147951, "metrics": {"system": {"cpu": {"usage": 0.057499999999999996, "temperature": 37, "cores": 10}, "memory": {"used": 22335976, "total": 17179869184, "efficiency": 5.262002421386384}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749449348022}, "performance": {"uptime": 1799.08, "efficiency": 63.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749450949014, "cpu": 0.2742, "memory": 0.10534641332924366, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749450951015, "cpu": 0.019100000000000002, "memory": 0.10187122970819473, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749450953015, "cpu": 0.28869999999999996, "memory": 0.1020113006234169, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749450955017, "cpu": 0.0137, "memory": 0.10299114510416985, "temperature": 37, "efficiency": 70}, {"timestamp": 1749450957018, "cpu": 0.021900000000000003, "memory": 0.1029105857014656, "temperature": 37, "efficiency": 70}, {"timestamp": 1749450959019, "cpu": 0.5528, "memory": 0.10678009130060673, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749450961019, "cpu": 0.0126, "memory": 0.10653845965862274, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749450963019, "cpu": 0.0135, "memory": 0.10374910198152065, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749450965019, "cpu": 0.3834, "memory": 0.10589486919343472, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749450967020, "cpu": 0.0113, "memory": 0.10520746000111103, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749450969022, "cpu": 0.1363, "memory": 0.104151526466012, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749450971023, "cpu": 0.3248, "memory": 0.10593850165605545, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749450973024, "cpu": 0.0164, "memory": 0.10723727755248547, "temperature": 37, "efficiency": 69}, {"timestamp": 1749450975024, "cpu": 0.0215, "memory": 0.10799067094922066, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749450977025, "cpu": 0.33080000000000004, "memory": 0.10890490375459194, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749450979027, "cpu": 0.0091, "memory": 0.10681822896003723, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749450981026, "cpu": 0.0201, "memory": 0.10543134994804859, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749450983028, "cpu": 0.025, "memory": 0.10877689346671104, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749450985030, "cpu": 0.8824000000000001, "memory": 0.10702800936996937, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749450987030, "cpu": 0.025300000000000003, "memory": 0.11019888333976269, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749450989031, "cpu": 0.0196, "memory": 0.1083355862647295, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749450991032, "cpu": 0.0621, "memory": 0.10748635977506638, "temperature": 37, "efficiency": 70}, {"timestamp": 1749450993033, "cpu": 0.0237, "memory": 0.1082555390894413, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749450995034, "cpu": 0.0167, "memory": 0.10977094061672688, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749450997035, "cpu": 0.0115, "memory": 0.107939587906003, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749450999036, "cpu": 0.0176, "memory": 0.11131055653095245, "temperature": 37, "efficiency": 68}, {"timestamp": 1749451001037, "cpu": 0.0139, "memory": 0.10741343721747398, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749451003038, "cpu": 0.0102, "memory": 0.10821246542036533, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749451005040, "cpu": 0.017499999999999998, "memory": 0.11180490255355835, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749451007040, "cpu": 0.0106, "memory": 0.10866252705454826, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749451009042, "cpu": 0.0107, "memory": 0.10998984798789024, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749451011044, "cpu": 0.025099999999999997, "memory": 0.10896720923483372, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749451013044, "cpu": 0.8733, "memory": 0.11184918694198132, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749451015046, "cpu": 0.0107, "memory": 0.11045061983168125, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749451017047, "cpu": 0.46620000000000006, "memory": 0.11373781599104404, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749451019048, "cpu": 0.2483, "memory": 0.11130431666970253, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749451021049, "cpu": 0.0106, "memory": 0.11127269826829433, "temperature": 37, "efficiency": 68}, {"timestamp": 1749451023050, "cpu": 1.2779, "memory": 0.11468268930912018, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451025051, "cpu": 0.2802, "memory": 0.11213519610464573, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749451027052, "cpu": 0.013300000000000001, "memory": 0.11232099495828152, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749451029052, "cpu": 0.265, "memory": 0.11245631612837315, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749451031053, "cpu": 0.4372, "memory": 0.11585387401282787, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451033054, "cpu": 0.6836, "memory": 0.11564553715288639, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451035055, "cpu": 0.40049999999999997, "memory": 0.11403658427298069, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749451037056, "cpu": 0.31020000000000003, "memory": 0.11690468527376652, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749451039055, "cpu": 0.0075, "memory": 0.1174677163362503, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749451041057, "cpu": 0.2859, "memory": 0.11621410958468914, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451043057, "cpu": 1.1312, "memory": 0.11453181505203247, "temperature": 37, "efficiency": 68}, {"timestamp": 1749451045058, "cpu": 0.6406000000000001, "memory": 0.11473610065877438, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749451047058, "cpu": 0.4413, "memory": 0.11774897575378418, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749451049058, "cpu": 0.45599999999999996, "memory": 0.11590099893510342, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451051058, "cpu": 0.1137, "memory": 0.11632237583398819, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749451053061, "cpu": 1.1384, "memory": 0.11623525060713291, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749451055061, "cpu": 0.6056, "memory": 0.11740094050765038, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749451057062, "cpu": 0.09079999999999999, "memory": 0.11742925271391869, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749451059064, "cpu": 0.0749, "memory": 0.12040860019624233, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451061065, "cpu": 0.5781999999999999, "memory": 0.12126653455197811, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451063064, "cpu": 0.2228, "memory": 0.12023253366351128, "temperature": 37, "efficiency": 67}, {"timestamp": 1749451065065, "cpu": 0.0619, "memory": 0.11771619319915771, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749451067065, "cpu": 0.43499999999999994, "memory": 0.11878018267452717, "temperature": 37, "efficiency": 66}, {"timestamp": 1749451069067, "cpu": 0.11299999999999999, "memory": 0.11915769428014755, "temperature": 37, "efficiency": 66}, {"timestamp": 1749451071066, "cpu": 0.09759999999999999, "memory": 0.11866013519465923, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749451073068, "cpu": 0.9178999999999999, "memory": 0.1208762638270855, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451075068, "cpu": 0.07339999999999999, "memory": 0.1212047878652811, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749451077070, "cpu": 0.0782, "memory": 0.12038256973028183, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451079071, "cpu": 0.6061000000000001, "memory": 0.12365086004137993, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451081072, "cpu": 0.0897, "memory": 0.12063104659318924, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451083073, "cpu": 0.4952, "memory": 0.12336564250290394, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451085073, "cpu": 0.3869, "memory": 0.12109666131436825, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749451087074, "cpu": 0.0809, "memory": 0.12382389977574348, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451089075, "cpu": 0.0639, "memory": 0.12463578023016453, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749451091076, "cpu": 0.4675, "memory": 0.12132707051932812, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749451093077, "cpu": 0.2985, "memory": 0.12375782243907452, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451095078, "cpu": 0.064, "memory": 0.12394203804433346, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749451097079, "cpu": 0.2329, "memory": 0.12466311454772949, "temperature": 37, "efficiency": 66}, {"timestamp": 1749451099080, "cpu": 0.0328, "memory": 0.1246139407157898, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749451101081, "cpu": 0.0388, "memory": 0.12219827622175217, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749451103082, "cpu": 0.28769999999999996, "memory": 0.12329770252108574, "temperature": 37, "efficiency": 65}, {"timestamp": 1749451105083, "cpu": 0.0417, "memory": 0.12368429452180862, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749451107084, "cpu": 0.0335, "memory": 0.12645553797483444, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451109085, "cpu": 0.2995, "memory": 0.12517091818153858, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451111086, "cpu": 0.0489, "memory": 0.12350445613265038, "temperature": 37, "efficiency": 65}, {"timestamp": 1749451113099, "cpu": 0.3116, "memory": 0.1259115058928728, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451115088, "cpu": 0.2878, "memory": 0.12470833025872707, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749451117086, "cpu": 0.06620000000000001, "memory": 0.12712273746728897, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451119089, "cpu": 0.06130000000000001, "memory": 0.12410692870616913, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749451121091, "cpu": 0.3513, "memory": 0.12492826208472252, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451123091, "cpu": 0.25279999999999997, "memory": 0.12768139131367207, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749451125092, "cpu": 0.0804, "memory": 0.12691346928477287, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451127093, "cpu": 0.4134, "memory": 0.12795855291187763, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749451129095, "cpu": 0.09, "memory": 0.1277684699743986, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451131096, "cpu": 0.0665, "memory": 0.12565176002681255, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749451133096, "cpu": 0.8215999999999999, "memory": 0.1263835933059454, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749451135097, "cpu": 0.0678, "memory": 0.12692478485405445, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749451137097, "cpu": 0.10070000000000001, "memory": 0.1293533481657505, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749451139099, "cpu": 0.4908, "memory": 0.12831762433052063, "temperature": 37, "efficiency": 64}, {"timestamp": 1749451141099, "cpu": 0.053700000000000005, "memory": 0.12641330249607563, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749451143100, "cpu": 0.4869, "memory": 0.12917951680719852, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749451145101, "cpu": 0.3881, "memory": 0.12713512405753136, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749451147102, "cpu": 0.057499999999999996, "memory": 0.1300124917179346, "temperature": 37, "efficiency": 63.7}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}