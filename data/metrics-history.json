{"timestamp": 1749468248071, "metrics": {"system": {"cpu": {"usage": 0.012400000000000001, "temperature": 37, "cores": 10}, "memory": {"used": 33011088, "total": 17179869184, "efficiency": 19.05034495530333}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749449348022}, "performance": {"uptime": 18899.233, "efficiency": 68.3, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749468049055, "cpu": 0.0195, "memory": 0.20480966195464134, "temperature": 37, "efficiency": 65}, {"timestamp": 1749468051055, "cpu": 0.055099999999999996, "memory": 0.20761406049132347, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749468053056, "cpu": 0.0144, "memory": 0.20469464361667633, "temperature": 37, "efficiency": 65}, {"timestamp": 1749468055056, "cpu": 0.0073, "memory": 0.20851260051131248, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749468057058, "cpu": 0.043800000000000006, "memory": 0.20693442784249783, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749468059058, "cpu": 0.0317, "memory": 0.2051022369414568, "temperature": 37, "efficiency": 65}, {"timestamp": 1749468061059, "cpu": 0.0142, "memory": 0.20829536952078342, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749468063059, "cpu": 0.046, "memory": 0.2061293926090002, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749468065060, "cpu": 0.0105, "memory": 0.20918543450534344, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749468067060, "cpu": 0.0283, "memory": 0.20721759647130966, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749468069062, "cpu": 0.1787, "memory": 0.21110055968165398, "temperature": 37, "efficiency": 64}, {"timestamp": 1749468071063, "cpu": 0.0114, "memory": 0.20821336656808853, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749468073065, "cpu": 0.0117, "memory": 0.20725782960653305, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749468075065, "cpu": 0.0458, "memory": 0.21178540773689747, "temperature": 37, "efficiency": 64}, {"timestamp": 1749468077067, "cpu": 0.0103, "memory": 0.20814910531044006, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749468079068, "cpu": 0.036699999999999997, "memory": 0.20768526010215282, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749468081068, "cpu": 0.022000000000000002, "memory": 0.21141394972801208, "temperature": 37, "efficiency": 64}, {"timestamp": 1749468083069, "cpu": 0.0077, "memory": 0.20862314850091934, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749468085070, "cpu": 2.2089000000000003, "memory": 0.2135478425770998, "temperature": 37, "efficiency": 63}, {"timestamp": 1749468087071, "cpu": 0.0335, "memory": 0.21228846162557602, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749468089072, "cpu": 0.016300000000000002, "memory": 0.21535386331379414, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749468091072, "cpu": 0.0202, "memory": 0.21200450137257576, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749468093078, "cpu": 0.0312, "memory": 0.21578222513198853, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749468095078, "cpu": 0.0041, "memory": 0.21263128146529198, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468097079, "cpu": 0.0069, "memory": 0.21123518235981464, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749468099078, "cpu": 0.05910000000000001, "memory": 0.2153799869120121, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749468101181, "cpu": 0.013300000000000001, "memory": 0.21243239752948284, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468103182, "cpu": 0.006600000000000001, "memory": 0.21485420875251293, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749468105182, "cpu": 0.0088, "memory": 0.21438924595713615, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749468107184, "cpu": 0.0084, "memory": 0.2166583202779293, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749468109186, "cpu": 0.4545, "memory": 0.21619456820189953, "temperature": 37, "efficiency": 63.2}, {"timestamp": 1749468111186, "cpu": 0.0156, "memory": 0.21361070685088634, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468113186, "cpu": 0.0072, "memory": 0.21553318947553635, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749468115186, "cpu": 0.005, "memory": 0.2141609787940979, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749468117188, "cpu": 0.0097, "memory": 0.21767010912299156, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749468119188, "cpu": 0.0081, "memory": 0.21430826745927334, "temperature": 37, "efficiency": 64}, {"timestamp": 1749468121189, "cpu": 0.0112, "memory": 0.21790475584566593, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749468123190, "cpu": 0.0172, "memory": 0.2152510453015566, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468125190, "cpu": 0.0087, "memory": 0.2182848285883665, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749468127191, "cpu": 0.0113, "memory": 0.21581565961241722, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749468129193, "cpu": 0.011000000000000001, "memory": 0.21602711640298367, "temperature": 37, "efficiency": 64}, {"timestamp": 1749468131193, "cpu": 0.0144, "memory": 0.217349361628294, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749468133194, "cpu": 0.0060999999999999995, "memory": 0.21602381020784378, "temperature": 37, "efficiency": 64}, {"timestamp": 1749468135195, "cpu": 0.007899999999999999, "memory": 0.22099209018051624, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749468137196, "cpu": 0.0097, "memory": 0.21827970631420612, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749468139197, "cpu": 0.0101, "memory": 0.21690018475055695, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468141198, "cpu": 0.007899999999999999, "memory": 0.2201637253165245, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749468143198, "cpu": 0.018799999999999997, "memory": 0.22188141010701656, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749468145199, "cpu": 0.021900000000000003, "memory": 0.22160089574754238, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749468147200, "cpu": 0.008, "memory": 0.22411984391510487, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468149201, "cpu": 0.01, "memory": 0.22229808382689953, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749468151203, "cpu": 0.0118, "memory": 0.21979697048664093, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749468153204, "cpu": 0.0115, "memory": 0.22371895611286163, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749468155206, "cpu": 0.0091, "memory": 0.22080321796238422, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749468157207, "cpu": 0.0075, "memory": 0.22426475770771503, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749468159208, "cpu": 0.0083, "memory": 0.22309813648462296, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749468161209, "cpu": 0.0075, "memory": 0.22502820938825607, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749468163210, "cpu": 0.0155, "memory": 0.22264518775045872, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749468165210, "cpu": 0.0097, "memory": 0.22218390367925167, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749468167211, "cpu": 0.0111, "memory": 0.2242786344140768, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749468169212, "cpu": 0.0073, "memory": 0.22262916900217533, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749468171213, "cpu": 0.0119, "memory": 0.2252168022096157, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749468173214, "cpu": 0.0118, "memory": 0.22762767039239407, "temperature": 37, "efficiency": 63.2}, {"timestamp": 1749468175215, "cpu": 0.015200000000000002, "memory": 0.2257936168462038, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749468177217, "cpu": 0.0093, "memory": 0.17854799516499043, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749468179218, "cpu": 0.0104, "memory": 0.18147281371057034, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749468181218, "cpu": 0.0127, "memory": 0.17934893257915974, "temperature": 37, "efficiency": 70}, {"timestamp": 1749468183220, "cpu": 0.0145, "memory": 0.18244371749460697, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749468185221, "cpu": 0.0059, "memory": 0.17930436879396439, "temperature": 37, "efficiency": 70}, {"timestamp": 1749468187222, "cpu": 0.005399999999999999, "memory": 0.18206709064543247, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749468189222, "cpu": 0.0058, "memory": 0.18142461776733398, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749468191224, "cpu": 0.006999999999999999, "memory": 0.1827666535973549, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749468193225, "cpu": 0.0046, "memory": 0.18071886152029037, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749468195225, "cpu": 0.004699999999999999, "memory": 0.18001478165388107, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749468197227, "cpu": 0.0045000000000000005, "memory": 0.18236502073705196, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749468199228, "cpu": 0.0068, "memory": 0.18040449358522892, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749468201228, "cpu": 0.0088, "memory": 0.18423604778945446, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749468203229, "cpu": 0.019799999999999998, "memory": 0.1804614905267954, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749468205230, "cpu": 0.011000000000000001, "memory": 0.18454408273100853, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749468207231, "cpu": 0.006600000000000001, "memory": 0.1873308327049017, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749468209233, "cpu": 0.0063999999999999994, "memory": 0.19056531600654125, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749468211233, "cpu": 0.0134, "memory": 0.18879137933254242, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749468213234, "cpu": 0.0117, "memory": 0.18709516152739525, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749468215234, "cpu": 0.0072, "memory": 0.1841262448579073, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749468217236, "cpu": 0.0136, "memory": 0.18771006725728512, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749468219236, "cpu": 0.0121, "memory": 0.1865607686340809, "temperature": 37, "efficiency": 69}, {"timestamp": 1749468221237, "cpu": 0.0071, "memory": 0.18852856010198593, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749468223238, "cpu": 0.0116, "memory": 0.18665604293346405, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749468225240, "cpu": 0.0117, "memory": 0.18640728667378426, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749468227242, "cpu": 0.0144, "memory": 0.1877313945442438, "temperature": 37, "efficiency": 69}, {"timestamp": 1749468229242, "cpu": 0.014899999999999998, "memory": 0.1864925492554903, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749468231244, "cpu": 0.0144, "memory": 0.18946374766528606, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749468233246, "cpu": 0.015300000000000001, "memory": 0.19064731895923615, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749468235247, "cpu": 0.013999999999999999, "memory": 0.18975529819726944, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749468237248, "cpu": 0.0144, "memory": 0.18791034817695618, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749468239250, "cpu": 0.012799999999999999, "memory": 0.19050478003919125, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749468241251, "cpu": 0.0127, "memory": 0.1881701871752739, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749468243253, "cpu": 0.0127, "memory": 0.191581342369318, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749468245254, "cpu": 0.015799999999999998, "memory": 0.1894547138363123, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749468247255, "cpu": 0.012400000000000001, "memory": 0.1921498216688633, "temperature": 37, "efficiency": 68.3}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}